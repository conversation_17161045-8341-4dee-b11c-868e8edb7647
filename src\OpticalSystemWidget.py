from PyQt5 import QtCore, QtGui
from PyQt5.QtWidgets import QWidget, QVBoxLayout
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

import sys
import math
import os
import numpy as np
import matplotlib
matplotlib.use("Qt5Agg")
# 添加中文字体支持
from matplotlib import rcParams
import platform

# 根据操作系统设置合适的中文字体
system = platform.system()
if system == 'Windows':
    # Windows系统
    font_list = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS']
elif system == 'Darwin':
    # macOS系统
    font_list = ['Heiti SC', 'Hiragino Sans GB', 'STHeiti', 'Apple LiGothic Medium']
else:
    # Linux系统
    font_list = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'Source Han Sans CN']

# 设置字体配置
rcParams['font.sans-serif'] = font_list
# 解决负号'-'显示为方块的问题
rcParams['axes.unicode_minus'] = False

# 尝试注册系统中的中文字体
try:
    from matplotlib.font_manager import fontManager, FontProperties
    # 查找本地字体文件位置
    font_files = []
    
    # 检查是否有本地字体文件
    possible_font_files = [
        # Windows字体路径
        "C:/Windows/Fonts/simhei.ttf",  # 黑体
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
        # 项目自带字体（如果有）
        "./fonts/simhei.ttf",
        "./fonts/simsun.ttc",
        "./src/fonts/simhei.ttf"
    ]
    
    for font_path in possible_font_files:
        if os.path.exists(font_path):
            font_files.append(font_path)
            print(f"找到字体文件: {font_path}")
    
    # 检查是否找到可用字体
    if font_files:
        # 选择第一个可用字体
        custom_font = font_files[0]
        fontManager.addfont(custom_font)
        print(f"成功加载字体: {custom_font}")
        # 设置为默认字体
        matplotlib.rcParams['font.family'] = FontProperties(fname=custom_font).get_name()
    else:
        print("未找到可用的中文字体文件，将使用系统配置")
        
    # 验证字体是否可用
    font_available = False
    for font in font_list:
        try:
            FontProperties(family=font)
            print(f"中文字体可用: {font}")
            font_available = True
            break
        except:
            continue
    
    if not font_available:
        print("警告: 未找到合适的中文字体，可能导致中文显示异常")
except Exception as e:
    print(f"字体处理过程出错: {str(e)}")
    print("将使用系统默认字体")

from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import matplotlib.patches as patches

class OpticalSystemWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 创建Figure对象 - 优化尺寸比例
        self.figure = Figure(figsize=(12, 8), dpi=100)
        # 创建FigureCanvas对象
        self.canvas = FigureCanvas(self.figure)
        # 创建布局管理器
        layout = QVBoxLayout()
        # 将画布添加到布局中
        layout.addWidget(self.canvas)
        # 设置窗口的布局
        self.setLayout(layout)
        # 创建轴对象
        self.ax = self.figure.add_subplot(111)
        # 设置坐标轴标签
        self.ax.set_xlabel('Z轴 (mm)', fontsize=12)
        self.ax.set_ylabel('Y轴 (mm)', fontsize=12)
        # 设置等比例显示
        self.ax.set_aspect('equal')
        # 显示网格
        self.ax.grid(True)
        # 镜片数据存储
        self.lens_data = []
        # 主镜索引
        self.primary_mirror_index = -1
    
    def set_lens_data(self, lens_data, primary_mirror_index):
        """设置镜片数据和主镜索引"""
        self.lens_data = lens_data
        self.primary_mirror_index = primary_mirror_index
        self.plot_optical_system()
    
    def plot_optical_system(self):
        """绘制光学系统图"""
        # 清除当前图形
        self.ax.clear()
        
        # 重新设置坐标轴标签和网格
        self.ax.set_xlabel('Z轴 (mm)', fontsize=12)
        self.ax.set_ylabel('Y轴 (mm)', fontsize=12)
        self.ax.grid(True)
        
        # 添加标题 - 确保中文正确显示，尝试多种字体
        try:
            # 使用配置好的字体
            self.ax.set_title('光学系统示意图', fontsize=14)
        except:
            # 如果出错，尝试直接使用特定字体
            try:
                # Windows的常用字体
                self.ax.set_title('光学系统示意图', fontsize=14, fontfamily='SimHei')
            except:
                # 最后使用不指定字体的方式
                self.ax.set_title('光学系统示意图', fontsize=14)
                print("警告: 标题中文字体设置失败，可能导致显示异常")
        
        # 如果没有数据或没有确定主镜，则直接返回
        if not self.lens_data or self.primary_mirror_index < 0:
            self.canvas.draw()
            return
        
        # 计算各个镜片的位置
        self.calculate_positions()
        
        # 绘制各个镜片
        self.draw_mirrors()
        
        # 自动调整坐标轴范围
        self.auto_adjust_limits()
        
        # 添加标尺
        self.add_scale()
        
        # 增加空白区域以便标签清晰显示
        self.ax.margins(0.1)
        
        # 自动调整布局
        self.figure.tight_layout()
        
        # 更新画布
        self.canvas.draw()
    
    def calculate_positions(self):
        """计算各个镜片相对于主镜的位置"""
        try:
            # 获取主镜位置作为原点
            primary_mirror = self.lens_data[self.primary_mirror_index]
            primary_z = 0  # 主镜Z坐标为0
            
            # 为每个镜片添加z坐标（相对于主镜）
            for i, lens in enumerate(self.lens_data):
                try:
                    if i == 0:
                        # 第一个镜片（通常是物面）
                        thicknesses = []
                        for j in range(0, self.primary_mirror_index):
                            try:
                                thickness = float(self.lens_data[j].get('Thickness', 0))
                                if np.isfinite(thickness):
                                    thicknesses.append(thickness)
                            except (ValueError, TypeError):
                                pass
                        lens['z_pos'] = primary_z - sum(thicknesses)
                    elif i <= self.primary_mirror_index:
                        # 主镜之前的镜片
                        thicknesses = []
                        for j in range(i, self.primary_mirror_index):
                            try:
                                thickness = float(self.lens_data[j].get('Thickness', 0))
                                if np.isfinite(thickness):
                                    thicknesses.append(thickness)
                            except (ValueError, TypeError):
                                pass
                        lens['z_pos'] = primary_z - sum(thicknesses)
                    else:
                        # 主镜之后的镜片
                        thicknesses = []
                        for j in range(self.primary_mirror_index, i):
                            try:
                                thickness = float(self.lens_data[j].get('Thickness', 0))
                                if np.isfinite(thickness):
                                    thicknesses.append(thickness)
                            except (ValueError, TypeError):
                                pass
                        lens['z_pos'] = primary_z + sum(thicknesses)
                except Exception as e:
                    # 计算单个镜片位置出错时，使用默认位置
                    print(f"计算镜片 {i} 位置时出错: {str(e)}")
                    lens['z_pos'] = primary_z + i - self.primary_mirror_index
        except Exception as e:
            print(f"计算镜片位置时出错: {str(e)}")
            # 如果计算出错，为每个镜片分配简单的位置
            for i, lens in enumerate(self.lens_data):
                lens['z_pos'] = i * 20  # 简单地沿Z轴均匀分布
    
    def draw_mirrors(self):
        """绘制所有镜片"""
        try:
            # 用于记录前一个镜面
            prev_lens = None
            
            for i, lens in enumerate(self.lens_data):
                try:
                    # 跳过coordinate break类型的表面
                    if "Coordinate Break" in str(lens.get('Surface Type', '')):
                        continue
                    
                    # 获取镜片参数
                    z_pos = lens.get('z_pos', 0)
                    material = lens.get('Material', '')
                    
                    # 判断镜面类型
                    is_reference_surface = False
                    is_back_surface = False
                    
                    # 无材料属性的镜面可能是后表面或参考面
                    if not material or material.strip() == '':
                        # 如果前一个镜面有材料，当前是其后表面
                        if prev_lens and prev_lens.get('Material', ''):
                            is_back_surface = True
                        # 否则是参考面
                        else:
                            is_reference_surface = True
                    
                    # 如果是后表面，跳过绘制（已在前表面时绘制）
                    if is_back_surface:
                        prev_lens = lens
                        continue
                    
                    # 确保z_pos是一个有效的数值
                    if not isinstance(z_pos, (int, float)) or not np.isfinite(z_pos):
                        print(f"镜片 {i} 的z_pos无效: {z_pos}")
                        z_pos = i * 20  # 使用默认位置
                        
                    # 获取并验证直径
                    try:
                        diameter = 2 * float(lens.get('Clear Semi-Diameter', 10))
                        if not np.isfinite(diameter) or diameter <= 0:
                            print(f"镜片 {i} 的直径无效: {diameter}")
                            diameter = 20  # 使用默认直径
                    except (ValueError, TypeError):
                        print(f"无法转换镜片 {i} 的直径")
                        diameter = 20  # 使用默认直径
                        
                    # 处理特殊的Radius值
                    radius_str = str(lens.get('Radius', 'inf'))
                    try:
                        radius = float(radius_str) if radius_str.lower() != 'inf' else float('inf')
                        if not np.isfinite(radius) and radius_str.lower() != 'inf':
                            radius = float('inf')  # 如果radius不是有限值且不是'inf'，假定为平面
                    except ValueError:
                        radius = float('inf')  # 如果无法转换，假定为平面
                    
                    # 确定镜片是平面还是曲面
                    is_flat = abs(radius) >= 1e10 or abs(radius) < 1e-10  # 半径无穷大或为0视为平面
                    
                    # 绘制镜片
                    if is_reference_surface:
                        # 参考面只绘制一条虚线表示位置
                        self.ax.plot([z_pos, z_pos], [-diameter/2, diameter/2], 'k--', linewidth=1.5)
                    elif is_flat:
                        # 绘制平面镜片（垂直线段）
                        self.ax.plot([z_pos, z_pos], [-diameter/2, diameter/2], 'k-', linewidth=2)
                    else:
                        # 绘制曲面镜片
                        try:
                            # 绘制垂直线段表示镜片的位置和直径范围
                            self.ax.plot([z_pos, z_pos], [-diameter/2, diameter/2], 'k:', linewidth=1)
                            
                            # 计算曲率半径
                            r_abs = abs(radius)
                            
                            # 弧的跨度(基于镜片直径，但限制在合理范围内)
                            try:
                                sagitta = diameter**2 / (8 * r_abs)  # 镜面中心到弦的距离
                                if sagitta > r_abs:
                                    sagitta = r_abs * 0.5  # 限制在合理范围内
                                
                                # 确保弧的范围不超过镜片实际尺寸
                                arc_width = min(diameter, 2 * r_abs)
                            except (ValueError, ZeroDivisionError):
                                arc_width = diameter
                                sagitta = arc_width * 0.1  # 默认值
                            
                            # 确定曲面的方向
                            if radius > 0:  # 凸面朝右(Z轴正方向)
                                # 弧的起点和终点
                                arc_left = z_pos
                                arc_right = z_pos + sagitta
                                
                                # 绘制代表曲面的弧
                                arc_x = np.linspace(arc_left, arc_right, 50)
                                # 使用圆的方程计算y坐标
                                center_x = z_pos + radius
                                arc_y = np.sqrt(r_abs**2 - (arc_x - center_x)**2) * np.sign(diameter)
                                
                                # 绘制前表面曲线
                                self.ax.plot(arc_x, arc_y, 'k-', linewidth=2)
                                self.ax.plot(arc_x, -arc_y, 'k-', linewidth=2)
                            else:  # 凹面朝右(Z轴正方向)
                                # 弧的起点和终点
                                arc_left = z_pos - sagitta
                                arc_right = z_pos
                                
                                # 绘制代表曲面的弧
                                arc_x = np.linspace(arc_left, arc_right, 50)
                                # 使用圆的方程计算y坐标
                                center_x = z_pos + radius  # 注意：radius是负值
                                arc_y = np.sqrt(r_abs**2 - (arc_x - center_x)**2) * np.sign(diameter)
                                
                                # 绘制前表面曲线
                                self.ax.plot(arc_x, arc_y, 'k-', linewidth=2)
                                self.ax.plot(arc_x, -arc_y, 'k-', linewidth=2)
                            
                        except Exception as e:
                            # 如果计算失败，绘制为平面
                            print(f"绘制镜片 {i} 的曲面时出错: {str(e)}")
                            self.ax.plot([z_pos, z_pos], [-diameter/2, diameter/2], 'k-', linewidth=2)
                    
                    # 添加标签 - 简化为只显示ID号
                    try:
                        # 只显示ID
                        id_value = lens.get('ID', i)
                        label = f"ID:{id_value}"
                        
                        # 显示标签 - 调整标签位置和样式使其更加清晰
                        self.ax.text(z_pos, -diameter/2 - 2, label, 
                                    horizontalalignment='center',
                                    verticalalignment='top',
                                    fontsize=9,
                                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))
                    except Exception as e:
                        print(f"添加镜片 {i} 的标签时出错: {str(e)}")
                    
                    # 更新前一个镜面
                    prev_lens = lens
                except Exception as e:
                    print(f"处理镜片 {i} 时出错: {str(e)}")
                    prev_lens = lens
        except Exception as e:
            print(f"绘制镜片时出错: {str(e)}")
    
    def auto_adjust_limits(self):
        """自动调整坐标轴范围以适应所有镜片"""
        try:
            # 计算所有镜片的范围
            valid_surfaces = [lens for lens in self.lens_data if 'Coordinate Break' not in str(lens.get('Surface Type', ''))]
            
            if not valid_surfaces:
                # 如果没有有效的镜片，设置默认范围
                self.ax.set_xlim(-10, 10)
                self.ax.set_ylim(-10, 10)
                return
            
            # 获取z位置
            z_positions = [lens.get('z_pos', 0) for lens in valid_surfaces]
            # 过滤掉可能的NaN或Inf值
            z_positions = [z for z in z_positions if z is not None and np.isfinite(z)]
            
            if not z_positions:
                # 如果没有有效的z位置，设置默认范围
                self.ax.set_xlim(-10, 10)
                self.ax.set_ylim(-10, 10)
                return
                
            min_z = min(z_positions)
            max_z = max(z_positions)
            
            # 获取最大直径，同时过滤掉可能的NaN或Inf值
            diameters = []
            for lens in valid_surfaces:
                try:
                    diam = 2 * float(lens.get('Clear Semi-Diameter', 10))
                    if np.isfinite(diam) and diam > 0:
                        diameters.append(diam)
                except (ValueError, TypeError):
                    pass
            
            if not diameters:
                # 如果没有有效的直径，使用默认值
                max_diameter = 20
            else:
                max_diameter = max(diameters)
            
            # 添加边距 - 增加边距使图形更加清晰
            margin = max_diameter * 0.4
            
            # 设置Z轴范围
            z_range = max_z - min_z
            if z_range < 1e-10:  # 防止z_range太小
                z_range = 1.0
                
            self.ax.set_xlim(min_z - z_range * 0.15, max_z + z_range * 0.15)
            
            # 设置Y轴范围
            self.ax.set_ylim(-max_diameter/2 - margin, max_diameter/2 + margin)
        except Exception as e:
            # 出现任何错误时，设置默认范围
            print(f"设置坐标轴范围时出错: {str(e)}")
            self.ax.set_xlim(-10, 10)
            self.ax.set_ylim(-10, 10)
    
    def add_scale(self):
        """添加标尺"""
        try:
            # 获取当前坐标轴范围
            xmin, xmax = self.ax.get_xlim()
            ymin, ymax = self.ax.get_ylim()
            
            # 计算合适的刻度单位（根据图形大小动态调整）
            x_range = xmax - xmin
            y_range = ymax - ymin
            
            if x_range <= 0 or y_range <= 0 or not np.isfinite(x_range) or not np.isfinite(y_range):
                # 如果范围无效，使用默认刻度
                self.ax.xaxis.set_major_locator(plt.MultipleLocator(5))
                self.ax.yaxis.set_major_locator(plt.MultipleLocator(5))
            else:
                # X轴标尺间隔（Z轴方向）
                x_tick = 10 ** math.floor(math.log10(x_range / 5))
                if x_range / x_tick < 3:
                    x_tick /= 2
                elif x_range / x_tick > 10:
                    x_tick *= 2
                
                # Y轴标尺间隔
                y_tick = 10 ** math.floor(math.log10(y_range / 5))
                if y_range / y_tick < 3:
                    y_tick /= 2
                elif y_range / y_tick > 10:
                    y_tick *= 2
                
                # 设置刻度
                self.ax.xaxis.set_major_locator(plt.MultipleLocator(x_tick))
                self.ax.yaxis.set_major_locator(plt.MultipleLocator(y_tick))
            
            # 添加网格线 - 优化网格线样式
            self.ax.grid(True, linestyle='--', alpha=0.5)
            
            # 添加原点标记（主镜位置）
            self.ax.plot(0, 0, 'ro', markersize=6)
            
            # 使坐标轴刻度标签更加清晰
            self.ax.tick_params(axis='both', which='major', labelsize=10)
        except Exception as e:
            print(f"添加标尺时出错: {str(e)}")
            # 出错时使用默认设置
            self.ax.xaxis.set_major_locator(plt.MultipleLocator(5))
            self.ax.yaxis.set_major_locator(plt.MultipleLocator(5))
            self.ax.grid(True) 