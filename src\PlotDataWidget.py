from PyQt5 import QtCore, QtGui
from PyQt5.QtWidgets import  QFileDialog, QApplication, QWidget, QVBoxLayout
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

import sys
import matplotlib
matplotlib.use("Qt5Agg")
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import numpy as np

class PlotDataWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 创建Figure对象，设置图形大小等属性
        self.figure = Figure(figsize=(15, 4), dpi=100)
        # 创建FigureCanvas对象，将Figure关联到画布上
        self.canvas = FigureCanvas(self.figure)
        # 创建布局管理器
        layout = QVBoxLayout()
        # 将画布添加到布局中
        layout.addWidget(self.canvas)
        # 设置窗口的布局
        self.setLayout(layout)
        self.axes = []  # 存储多个子图的axes

    # 绘制二维数据
    def plot_grid_image(self, npData):
        """
        在Figure对象对应的子图上绘制图像并添加颜色条
        :param npData: 要显示的图像数据（numpy数组形式）
        """
        # 清空当前图形和颜色条
        self.figure.clear()
        ax = self.figure.add_subplot(111)  # Create a subplot
        # 使用np.flipud翻转数据（如果原始数据需要这样的处理，根据实际情况调整）
        flipped_data = np.flipud(npData)
        # 显示图像，设置颜色映射为'jet'，并设置图像显示的范围extent
        im = ax.imshow(flipped_data, cmap='jet', extent=[-1, 1, -1, 1])
        # 添加颜色条
        self.figure.colorbar(im, ax=ax)
        self.canvas.draw()

    # 绘制散点图
    def plot_scatter_data(self, field_index=0, xary=None, yary=None, wave_index=None):
        """
        绘制散点图
        :param field_index: 视场索引
        :param xary: X坐标数据
        :param yary: Y坐标数据
        :param wave_index: 波长索引，用于选择颜色
        """
        if field_index < len(self.axes) and xary is not None and yary is not None:
            ax = self.axes[field_index]
            
            # 根据波长选择不同的颜色
            colors = ('b', 'g', 'r', 'c', 'm', 'y', 'k')
            color = colors[wave_index % len(colors)] if wave_index is not None else 'b'
            
            # 绘制散点图
            ax.plot(xary, yary, '.', ms=1, color=color)
            
            # 自动调整坐标轴范围（可选）
            self._adjust_axis_limits(ax, xary, yary)
            
            self.canvas.draw()
            return True
        return False

    # 初始化散点图，支持多视场
    def init_scatter(self, num_fields=1, display_all=False, selected_field=0):
        """
        初始化散点图显示区域
        :param num_fields: 视场数量
        :param display_all: 是否显示所有视场
        :param selected_field: 选择的视场索引
        """
        self.figure.clear()
        self.axes = []
        
        if display_all and num_fields > 1:
            # 显示所有视场时，根据视场数量调整图像大小
            width = min(15, num_fields * 5)  # 最大宽度为15
            height = 4 * ((num_fields - 1) // 3 + 1)  # 每行最多3个视场，每个高度4
            self.figure.set_size_inches(width, height)
            
            # 计算子图的行列数
            num_cols = min(3, num_fields)  # 每行最多3个子图
            num_rows = (num_fields + num_cols - 1) // num_cols
            
            # 创建子图
            for i in range(num_fields):
                ax = self.figure.add_subplot(num_rows, num_cols, i+1, aspect='equal')
                ax.set_xlim(-20e-3, 20e-3)
                ax.set_ylim(-20e-3, 20e-3)
                ax.set_title(f'Field {i+1}')
                ax.grid(True)
                self.axes.append(ax)
        else:
            # 只显示单个视场
            self.figure.set_size_inches(8, 8)  # 单个视场用大一点的图
            ax = self.figure.add_subplot(111, aspect='equal')
            ax.set_xlim(-20e-3, 20e-3)
            ax.set_ylim(-20e-3, 20e-3)
            if display_all:
                ax.set_title(f'All Fields')
            else:
                ax.set_title(f'Field {selected_field+1}')
            ax.grid(True)
            self.axes.append(ax)
        
        # 调整子图之间的间距
        self.figure.tight_layout()

    # 辅助方法：自动调整坐标轴范围
    def _adjust_axis_limits(self, ax, xary, yary, margin_factor=0.1):
        """
        自动调整坐标轴范围，保持长宽比
        :param ax: 要调整的子图对象
        :param xary: X坐标数据
        :param yary: Y坐标数据
        :param margin_factor: 边距比例
        """
        try:
            x_min, x_max = np.min(xary), np.max(xary)
            y_min, y_max = np.min(yary), np.max(yary)
            
            # 如果数据范围太小，保留默认设置
            if abs(x_max - x_min) < 1e-6 or abs(y_max - y_min) < 1e-6:
                return
            
            # 计算边距
            margin_x = margin_factor * (x_max - x_min)
            margin_y = margin_factor * (y_max - y_min)
            
            # 设置新的坐标范围
            new_x_min = x_min - margin_x
            new_x_max = x_max + margin_x
            new_y_min = y_min - margin_y
            new_y_max = y_max + margin_y
            
            # 保持等比例
            x_range = new_x_max - new_x_min
            y_range = new_y_max - new_y_min
            if x_range > y_range:
                center_y = (new_y_min + new_y_max) / 2
                new_y_min = center_y - x_range / 2
                new_y_max = center_y + x_range / 2
            else:
                center_x = (new_x_min + new_x_max) / 2
                new_x_min = center_x - y_range / 2
                new_x_max = center_x + y_range / 2
            
            # 应用新的范围
            ax.set_xlim(new_x_min, new_x_max)
            ax.set_ylim(new_y_min, new_y_max)
        except Exception as e:
            # 如果出现异常，忽略并保留原始范围
            print(f"调整坐标轴范围时出错: {str(e)}")

    # 绘制折线图
    def plot_line_data(self, x, y, c='b', ls='-'):
        self.ax.plot(x, y, color=c, linestyle = ls)
        self.ax.grid(True)
        self.canvas.draw()
        pass

    def init_line_data(self, maxF=100):
        self.figure.clear()
        self.ax = self.figure.add_subplot(111)
        self.ax.set_xlim(0,maxF)
        self.ax.set_ylim(0, 1)
        pass