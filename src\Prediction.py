import sys
from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QTableWidget, QTableWidgetItem, QVBoxLayout, QHBoxLayout, QMessageBox, QInputDialog, QDialog, QComboBox, QDialogButtonBox
from enum import Enum

# 变量类型
class OptimizationItem(Enum):
    ZDIS = 1    # 对应zemax中的thickness，Z向距离
    RADIUS = 2  # test 对应zemax中的半径，装配分析，不优化半径，这里只是测试

class PredictionVariable:
    def __init__(self, mirror_id: int, optimization_item: OptimizationItem):
        self.mirror_id = mirror_id
        self.optimization_item = optimization_item

# 变量添加对话框，让用户选择: 镜子ID和变量类型
class AddVariableDialog(QDialog):
    def __init__(self, mirror_count, parent=None):
        super().__init__(parent)

        self.setWindowTitle("Add Prediction Variable")
        self.setGeometry(100, 100, 300, 150)

        self.mirror_id_combo_box = QComboBox()
        self.optimization_item_combo_box = QComboBox()

        for i in range(mirror_count):
            self.mirror_id_combo_box.addItem(str(i))

        for item in OptimizationItem:
            self.optimization_item_combo_box.addItem(item.name)

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout = QHBoxLayout()
        layout.addWidget(self.mirror_id_combo_box)
        layout.addWidget(self.optimization_item_combo_box)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def get_selected_values(self):
        mirror_id = int(self.mirror_id_combo_box.currentText())
        optimization_item = OptimizationItem[self.optimization_item_combo_box.currentText()]
        return mirror_id, optimization_item
