from statistics import pvariance

import clr, os, winreg, sys
from itertools import islice
import itertools
import numpy as np

class PythonStandaloneApplication(object):
    # 异常报错
    class LicenseException(Exception):
        pass

    class ConnectionException(Exception):
        pass

    class InitializationException(Exception):
        pass

    class SystemNotPresentException(Exception):
        pass

    def __init__(self, path=None):
        # clr引用zemax的c#动态库
        clr.AddReference(os.getcwd() + '\\ZOS\\ZOSAPI_NetHelper.dll')
        import ZOSAPI_NetHelper

        # Find the installed version of OpticStudio，默认路径初始化
        if path is None:
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize()
        else:
            # Note -- uncomment the following line to use a custom initialization path
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize(path)

        # add ZOS-API referencecs
        # 添加其它两个动态库
        clr.AddReference(os.getcwd() + '\\ZOS\\ZOSAPI.dll')
        clr.AddReference(os.getcwd() + '\\ZOS\\ZOSAPI_Interfaces.dll')
        import ZOSAPI

        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI

        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI

        # Create the initial connection class
        # 尝试与zemax服务器进行连接
        self.TheConnection = ZOSAPI.ZOSAPI_Connection()

        if self.TheConnection is None:
            raise PythonStandaloneApplication.ConnectionException("Unable to initialize .NET connection to ZOSAPI")

        self.TheApplication = self.TheConnection.CreateNewApplication()
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("Unable to acquire ZOSAPI application")

        if self.TheApplication.IsValidLicenseForAPI == False:
            raise PythonStandaloneApplication.LicenseException("License is not valid for ZOSAPI use")

        self.TheSystem = self.TheApplication.PrimarySystem
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")

    def __del__(self):
        if self.TheApplication is not None:
            self.TheApplication.CloseApplication()
            self.TheApplication = None

        self.TheConnection = None

    # 打开zemax文件
    def OpenFile(self, filepath, saveIfNeeded):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.LoadFile(filepath, saveIfNeeded)

    def CloseFile(self, save):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.Close(save)

    def reshape(self, data, x, y, transpose=False):
        """Converts a System.Double[,] to a 2D list for plotting or post processing

        Parameters
        ----------
        data      : System.Double[,] data directly from ZOS-API
        x         : x width of new 2D list [use var.GetLength(0) for dimension]
        y         : y width of new 2D list [use var.GetLength(1) for dimension]
        transpose : transposes data; needed for some multi-dimensional line series data

        Returns
        -------
        res       : 2D list; can be directly used with Matplotlib or converted to
                    a numpy array using numpy.asarray(res)
        """
        if type(data) is not list:
            data = list(data)
        var_lst = [y] * x;
        it = iter(data)
        res = [list(islice(it, i)) for i in var_lst]
        if transpose:
            return self.transpose(res);
        return res

    # 计算PV
    def calPV(self, data, filter_outliers=False, n_std=3):
        """
        计算波前数据的PV（峰谷值）
        
        参数:
        data - 输入的波前数据
        filter_outliers - 是否过滤异常值，默认False
        n_std - 过滤时使用的标准差倍数，默认3
        
        返回:
        pv - 峰谷值，如果计算失败返回0.0
        """
        try:
            # 转换为NumPy数组以提高性能
            if not isinstance(data, np.ndarray):
                if isinstance(data, list):
                    data_array = np.array(data)
                else:
                    # 尝试转换为列表再转为数组
                    data_array = np.array(list(data))
            else:
                data_array = data
                
            # 过滤NaN值
            data_array = data_array[~np.isnan(data_array)]
            
            # 检查过滤后的数据是否为空
            if data_array.size == 0:
                return 0.0
            
            # 是否需要过滤异常值
            if filter_outliers and data_array.size > 2:
                mean = np.mean(data_array)
                std = np.std(data_array)
                # 创建掩码，只保留在均值±n个标准差范围内的数据
                mask = np.abs(data_array - mean) <= n_std * std
                data_array = data_array[mask]
                
                # 再次检查过滤后是否为空
                if data_array.size == 0:
                    return 0.0
            
            # 使用NumPy的max和min函数计算PV值
            pv = np.max(data_array) - np.min(data_array)
            
            return pv
        except Exception as e:
            print(f"PV计算错误: {str(e)}")
            return 0.0  # 出错时返回默认值

    # 计算RMS
    def calRMS(self, data):
        try:
            # 将数据转换为列表并过滤NaN值
            if type(data) is not list:
                list_data = list(data)
            else:
                list_data = data
            
            filtered_list_data = [x for x in list_data if not np.isnan(x)]
            
            # 检查过滤后的数据是否为空
            if not filtered_list_data:
                return 0.0  # 返回默认值
            
            # 转换为NumPy数组以便进行计算
            data_array = np.array(filtered_list_data)
            
            # 计算平均值并去除（去除活塞项）
            data_mean = np.mean(data_array)
            data_centered = data_array - data_mean
            
            # 计算RMS（均方根）：平方，求平均，开方
            rms = np.sqrt(np.mean(data_centered ** 2))
            
            return rms
        except Exception as e:
            print(f"RMS计算错误: {str(e)}")
            return 0.0  # 出错时返回默认值

    def transpose(self, data):
        """Transposes a 2D list (Python3.x or greater).

        Useful for converting mutli-dimensional line series (i.e. FFT PSF)

        Parameters
        ----------
        data      : Python native list (if using System.Data[,] object reshape first)

        Returns
        -------
        res       : transposed 2D list
        """
        if type(data) is not list:
            data = list(data)
        return list(map(list, zip(*data)))