import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON>lication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QCheckBox, QComboBox, QPushButton, QSpinBox,QTableWidgetItem, QMessageBox
from Tolerance_Edit import ToleranceEditDialog

class SensivitityDialog(QDialog):
    def __init__(self, surfnum, parent=None):
        super().__init__(parent)

        self.setWindowTitle("Settings Dialog")
        self.layout = QVBoxLayout()

        # Options 一般设置
        self.options_label = QLabel("Options")
        self.layout.addWidget(self.options_label)
        self.start_at_row_label = QLabel("Start At Row:")
        self.start_at_row_edit = QLineEdit("1")
        self.test_wavelength_label = QLabel("Test Wavelength")
        self.test_wavelength_edit = QLineEdit("0.633")
        self.start_at_surface_label = QLabel("Start At Surface:")
        self.start_at_surface_combo = QComboBox()
        for i in range(surfnum):
            self.start_at_surface_combo.addItem(str(i + 1))
        self.start_at_surface_combo.setCurrentIndex(0)
        self.stop_at_surface_label = QLabel("Stop At Surface:")
        self.stop_at_surface_combo = QComboBox()
        for i in range(surfnum):
            self.stop_at_surface_combo.addItem(str(i + 1))
        self.stop_at_surface_combo.setCurrentIndex(1)

        options_layout = QVBoxLayout()
        start_at_row_layout = QHBoxLayout()
        start_at_row_layout.addWidget(self.start_at_row_label)
        start_at_row_layout.addWidget(self.start_at_row_edit)
        options_layout.addLayout(start_at_row_layout)
        test_wavelength_layout = QHBoxLayout()
        test_wavelength_layout.addWidget(self.test_wavelength_label)
        test_wavelength_layout.addWidget(self.test_wavelength_edit)
        options_layout.addLayout(test_wavelength_layout)
        start_at_surface_layout = QHBoxLayout()
        start_at_surface_layout.addWidget(self.start_at_surface_label)
        start_at_surface_layout.addWidget(self.start_at_surface_combo)
        options_layout.addLayout(start_at_surface_layout)
        stop_at_surface_layout = QHBoxLayout()
        stop_at_surface_layout.addWidget(self.stop_at_surface_label)
        stop_at_surface_layout.addWidget(self.stop_at_surface_combo)
        options_layout.addLayout(stop_at_surface_layout)
        self.layout.addLayout(options_layout)

        # Element Tolerances 装配公差
        self.element_tolerances_label = QLabel("Element Tolerances")
        self.layout.addWidget(self.element_tolerances_label)
        self.decenterX_check = QCheckBox("Decenter X")
        self.decenterX_edit = QLineEdit("0.2")
        self.decenterX_edit.setEnabled(False)
        self.decenterY_check = QCheckBox("Decenter Y")
        self.decenterY_edit = QLineEdit("0.2")
        self.decenterY_edit.setEnabled(False)
        self.tiltX_check = QCheckBox("Tilt X")
        self.tiltX_edit = QLineEdit("0.2")
        self.tiltX_edit.setEnabled(False)
        self.tiltY_check = QCheckBox("Tilt Y")
        self.tiltY_edit = QLineEdit("0.2")
        self.tiltY_edit.setEnabled(False)

        self.decenterX_check.stateChanged.connect(self.decenterX_check_changed)
        self.decenterY_check.stateChanged.connect(self.decenterY_check_changed)
        self.tiltX_check.stateChanged.connect(self.tiltX_check_changed)
        self.tiltY_check.stateChanged.connect(self.tiltY_check_changed)

        tolerances_layout = QVBoxLayout()
        decenterX_layout = QHBoxLayout()
        decenterX_layout.addWidget(self.decenterX_check)
        decenterX_layout.addWidget(self.decenterX_edit)
        tolerances_layout.addLayout(decenterX_layout)
        decenterY_layout = QHBoxLayout()
        decenterY_layout.addWidget(self.decenterY_check)
        decenterY_layout.addWidget(self.decenterY_edit)
        tolerances_layout.addLayout(decenterY_layout)
        tiltX_layout = QHBoxLayout()
        tiltX_layout.addWidget(self.tiltX_check)
        tiltX_layout.addWidget(self.tiltX_edit)
        tolerances_layout.addLayout(tiltX_layout)
        tiltY_layout = QHBoxLayout()
        tiltY_layout.addWidget(self.tiltY_check)
        tiltY_layout.addWidget(self.tiltY_edit)
        tolerances_layout.addLayout(tiltY_layout)
        self.layout.addLayout(tolerances_layout)

        # Zernike Irregularity 面形公差
        self.zernike_irregularity_check = QCheckBox("Zernike Irregularity")
        self.fringes_label = QLabel("Fringes:")
        self.fringes_edit = QLineEdit("0.2")
        self.fringes_edit.setEnabled(False)
        self.zernike_irregularity_check.stateChanged.connect(self.zernike_irregularity_check_changed)

        zernike_layout = QHBoxLayout()
        zernike_layout.addWidget(self.zernike_irregularity_check)
        zernike_layout.addWidget(self.fringes_label)
        zernike_layout.addWidget(self.fringes_edit)
        self.layout.addLayout(zernike_layout)

        # 敏感性分析参数设置
        self.criterion_label = QLabel("Criterion:")
        self.criterion_combo = QComboBox()
        self.criterion_combo.addItems(["RMS Spot Radius", "RMS Wavefront"])
        self.criterion_combo.setCurrentText("RMS Spot Radius")

        self.sampling_label = QLabel("Sampling:")
        self.sampling_combo = QComboBox()
        for i in range(1, 21):      # 与zemax一致
            self.sampling_combo.addItem(str(i))
        self.sampling_combo.setCurrentIndex(3)

        self.monte_carlo_label = QLabel("Monte Carlo Runs:")
        self.monte_carlo_spinbox = QSpinBox()
        self.monte_carlo_spinbox.setMinimum(10)
        self.monte_carlo_spinbox.setMaximum(10000)
        self.monte_carlo_spinbox.setValue(20)
        self.monte_carlo_spinbox.setSingleStep(1)

        new_elements_layout = QVBoxLayout()
        criterion_layout = QHBoxLayout()
        criterion_layout.addWidget(self.criterion_label)
        criterion_layout.addWidget(self.criterion_combo)
        new_elements_layout.addLayout(criterion_layout)
        sampling_layout = QHBoxLayout()
        sampling_layout.addWidget(self.sampling_label)
        sampling_layout.addWidget(self.sampling_combo)
        new_elements_layout.addLayout(sampling_layout)
        monte_carlo_layout = QHBoxLayout()
        monte_carlo_layout.addWidget(self.monte_carlo_label)
        monte_carlo_layout.addWidget(self.monte_carlo_spinbox)
        new_elements_layout.addLayout(monte_carlo_layout)
        self.layout.addLayout(new_elements_layout)

        # 修改按钮布局，添加Parameter Editing按钮
        button_layout = QHBoxLayout()
        
        # 添加Parameter Editing按钮
        self.param_edit_button = QPushButton("Parameter Editing")
        # 检查TDE是否已有数据并设置按钮状态
        self.check_and_set_button_state()
        self.param_edit_button.clicked.connect(self.open_tolerance_edit_dialog)

        # OK按钮
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.on_ok_clicked)
        
        button_layout.addWidget(self.param_edit_button)
        button_layout.addWidget(self.ok_button)
        self.layout.addLayout(button_layout)

        self.setLayout(self.layout)
        
        # 初始化tolerance数据存储
        self.updated_tolerance_data = None
        self.tde_data = []

        # 操作数类型映射字典
        self.operand_type_map = {
            32: 'COMP',  # 补偿器
            37: 'TWAV',  # 测试波长
            17: 'TEDX',  # X偏心
            18: 'TEDY',  # Y偏心
            20: 'TETX',  # X倾斜
            21: 'TETY',  # Y倾斜
            14: 'TEXI',  # 不规则性X
            41: 'TEZI',  # 不规则性Z
        }

    def decenterX_check_changed(self, state):
        self.decenterX_edit.setEnabled(state == 2)
        self.check_and_set_button_state()

    def decenterY_check_changed(self, state):
        self.decenterY_edit.setEnabled(state == 2)
        self.check_and_set_button_state()

    def tiltX_check_changed(self, state):
        self.tiltX_edit.setEnabled(state == 2)
        self.check_and_set_button_state()

    def tiltY_check_changed(self, state):
        self.tiltY_edit.setEnabled(state == 2)
        self.check_and_set_button_state()

    def zernike_irregularity_check_changed(self, state):
        self.fringes_edit.setEnabled(state == 2)
        self.check_and_set_button_state()

    def apply_wizard_settings(self):
        try:
            TheSystem = self.parent().TheSystem
            tde = TheSystem.TDE
            
            # 设置TDE模式
            tde.SetupMode = 1
            
            # 检查是否有任何选项被选中
            if not any([
                self.decenterX_check.isChecked(),
                self.decenterY_check.isChecked(),
                self.tiltX_check.isChecked(),
                self.tiltY_check.isChecked(),
                self.zernike_irregularity_check.isChecked()
            ]):
                print("没有选择任何公差选项")
                return False
            
            # 清除现有的操作数
            if tde.NumberOfOperands > 0:
                tde.RemoveOperandsAt(1, tde.NumberOfOperands)
                print("已清除现有操作数")
            
            
            
            # 如果没有更新的公差数据，则使用公差向导创建新的操作数
            tWiz = tde.SEQToleranceWizard
            
            # 设置表面范围和测试波长
            start_surface = int(self.start_at_surface_combo.currentText())
            stop_surface = int(self.stop_at_surface_combo.currentText())
            tWiz.StartAtSurface = start_surface
            tWiz.StopAtSurface = stop_surface
            tWiz.TestWavelength = float(self.test_wavelength_edit.text())
            
            print(f"设置表面范围: {start_surface} 到 {stop_surface}（包含）")
            
            # 首先禁用所有公差类型
            tWiz.IsElementDecenterXUsed = False
            tWiz.IsElementDecenterYUsed = False
            tWiz.IsElementTiltXUsed = False
            tWiz.IsElementTiltYUsed = False
            tWiz.IsSurfaceZernikeIrregularityUsed = False
            
            # 禁用其他不需要的公差类型
            tWiz.IsSurfaceRadiusUsed = False
            tWiz.IsSurfaceDecenterXUsed = False
            tWiz.IsSurfaceDecenterYUsed = False
            tWiz.IsSurfaceTiltXUsed = False
            tWiz.IsSurfaceTiltYUsed = False
            tWiz.IsSurfaceSandAIrregularityUsed = False
            tWiz.IsIndexUsed = False
            tWiz.IsSurfaceThicknessUsed = False
            tWiz.IsIndexAbbePercentageUsed = False
            
            # X偏心设置
            if self.decenterX_check.isChecked():
                value = self.decenterX_edit.text().strip()
                if value:
                    tWiz.ElementDecenterX = float(value)
                    tWiz.IsElementDecenterXUsed = True
                    print(f"已设置X偏心公差: {value}")
            
            # Y偏心设置
            if self.decenterY_check.isChecked():
                value = self.decenterY_edit.text().strip()
                if value:
                    tWiz.ElementDecenterY = float(value)
                    tWiz.IsElementDecenterYUsed = True
                    print(f"已设置Y偏心公差: {value}")
            
            # X倾斜设置
            if self.tiltX_check.isChecked():
                value = self.tiltX_edit.text().strip()
                if value:
                    tWiz.ElementTiltXDegrees = float(value)
                    tWiz.IsElementTiltXUsed = True
                    print(f"已设置X倾斜公差: {value}")
            
            # Y倾斜设置
            if self.tiltY_check.isChecked():
                value = self.tiltY_edit.text().strip()
                if value:
                    tWiz.ElementTiltYDegrees = float(value)
                    tWiz.IsElementTiltYUsed = True
                    print(f"已设置Y倾斜公差: {value}")
            
            # Zernike不规则性设置
            if self.zernike_irregularity_check.isChecked():
                value = self.fringes_edit.text().strip()
                if value:
                    tWiz.SurfaceZernikeIrregularityFringes = float(value)
                    tWiz.IsSurfaceZernikeIrregularityUsed = True
                    print(f"已设置Zernike不规则性公差: {value}")
            
            # 应用设置
            tWiz.OK()
            print(f"成功创建了 {tde.NumberOfOperands} 个公差操作数")
            
            return True
            
        except Exception as e:
            import traceback
            error_msg = traceback.format_exc()
            print(f"应用设置时出错: {error_msg}")
            QMessageBox.critical(self, "错误", f"应用公差向导设置时出错：\n{str(e)}")
            return False

    def open_tolerance_edit_dialog(self):
        """打开参数编辑对话框"""
        try:
            # 首先检查是否有选项被勾选
            if not any([
                self.decenterX_check.isChecked(),
                self.decenterY_check.isChecked(),
                self.tiltX_check.isChecked(),
                self.tiltY_check.isChecked(),
                self.zernike_irregularity_check.isChecked()
            ]):
                QMessageBox.warning(self, "警告", "请先选择至少一个公差选项。")
                return

            TheSystem = self.parent().TheSystem
            tde = TheSystem.TDE
            tde.TestWavelength=self.test_wavelength_edit.text()
            
            # 设置TDE模式
            tde.SetupMode = 1
            
            # 应用公差向导设置
            if not self.apply_wizard_settings():
                return
            
            self.tde_data = []
            print(f"操作数总数: {tde.NumberOfOperands}")
            
            for i in range(1, tde.NumberOfOperands + 1):
                operand = tde.GetOperandAt(i)
                operand_type = int(operand.Type)
                param1 = operand.Param1 if hasattr(operand, 'Param1') else None
                param2 = operand.Param2 if hasattr(operand, 'Param2') else None
                param3 = operand.Param3 if hasattr(operand, 'Param3') else None
                
                print(f"\n详细调试信息 - 操作数 {i}:")
                print(f"  原始类型编号: {operand_type}")
                print(f"  Param1: {param1}")
                print(f"  Param2: {param2}")
                
                try:
                    # 使用类中定义的映射字典
                    type_str = self.operand_type_map.get(operand_type, 'COMP')
                    
                    # 获取基本参数
                    Nominal = operand.Nominal if hasattr(operand, 'Nominal') else None
                    min_val = operand.Min if hasattr(operand, 'Min') else None
                    max_val = operand.Max if hasattr(operand, 'Max') else None
                    comment = operand.Comment if hasattr(operand, 'Comment') else None
                    
                    # 根据不同类型构建数据结构
                    if type_str == 'TWAV':
                        operand_data = {
                            'Type': type_str,
                            'Wave': tde.TestWavelength if tde.TestWavelength is not None else '',
                            'Comment': str(comment) if comment else ''
                        }
                    elif type_str in ['TEXI', 'TEZI']:
                        operand_data = {
                            'Type': type_str,
                            'Surf': str(param1) if param1 is not None else '',
                            'Max#': str(param2) if param2 is not None else '',
                            'Min#': str(param3) if param3 is not None else '',
                            'Nominal': str(Nominal) if Nominal is not None else '',
                            'Min': str(min_val) if min_val is not None else '',
                            'Max': str(max_val) if max_val is not None else '',
                            'Comment': str(comment) if comment else ''
                        }
                    elif type_str == 'COMP':
                        operand_data = {
                            'Type': type_str,
                            'Surf': str(param1) if param1 is not None else '',
                            'Code': str(param2) if param2 is not None else '',
                            'Nominal': str(Nominal) if Nominal is not None else '',
                            'Min': str(min_val) if min_val is not None else '',
                            'Max': str(max_val) if max_val is not None else '',
                            'Comment': str(comment) if comment else ''
                        }
                    else:  # TEDX, TEDY, TETX, TETY
                        operand_data = {
                            'Type': type_str,
                            'Surf1': str(param1) if param1 is not None else '',
                            'Surf2': str(param2) if param2 is not None else '',
                            'Nominal': str(Nominal) if Nominal is not None else '',
                            'Min': str(min_val) if min_val is not None else '',
                            'Max': str(max_val) if max_val is not None else '',
                            'Comment': str(comment) if comment else ''
                        }
                    
                    print(f"  操作数数据: {operand_data}")
                    self.tde_data.append(operand_data)
                    
                except Exception as e:
                    print(f"处理操作数 {i} 时出错: {str(e)}")
                    continue
            
            # 打开ToleranceEditDialog并传入当前数据
            if self.tde_data:  # 只有在有数据时才打开对话框
                print(f"Opening dialog with {len(self.tde_data)} operands")  # 调试输出
                self.tolerance_edit_dialog = ToleranceEditDialog(self.tde_data, self)
                self.tolerance_edit_dialog.data_updated.connect(self.receive_tolerance_data)
                self.tolerance_edit_dialog.exec_()
            else:
                QMessageBox.warning(self, "警告", "没有可编辑的公差数据。")
            
        except Exception as e:
            import traceback
            error_msg = traceback.format_exc()
            print(f"Error: {error_msg}")  # 调试输出
            QMessageBox.critical(self, "错误", f"打开参数编辑对话框时出错：\n{str(e)}")

    def receive_tolerance_data(self, data):
        """接收ToleranceEditDialog传递的数据"""
        self.updated_tolerance_data = data
        print("Updated Tolerance Data received:", self.updated_tolerance_data)

    def get_sensitivity_settings(self):
        """获取所有设置参数"""
        settings = {
            'criterion': self.criterion_combo.currentText(),
            'sampling': int(self.sampling_combo.currentText()),
            'monte_carlo_runs': self.monte_carlo_spinbox.value(),
            'start_surface': int(self.start_at_surface_combo.currentText()),
            'stop_surface': int(self.stop_at_surface_combo.currentText()),
            'test_wavelength': float(self.test_wavelength_edit.text()),
            'decenterX': {
                'enabled': self.decenterX_check.isChecked(),
                'value': float(self.decenterX_edit.text()) if self.decenterX_check.isChecked() else 0.0
            },
            'decenterY': {
                'enabled': self.decenterY_check.isChecked(),
                'value': float(self.decenterY_edit.text()) if self.decenterY_check.isChecked() else 0.0
            },
            'tiltX': {
                'enabled': self.tiltX_check.isChecked(),
                'value': float(self.tiltX_edit.text()) if self.tiltX_check.isChecked() else 0.0
            },
            'tiltY': {
                'enabled': self.tiltY_check.isChecked(),
                'value': float(self.tiltY_edit.text()) if self.tiltY_check.isChecked() else 0.0
            },
            'zernike_irregularity': {
                'enabled': self.zernike_irregularity_check.isChecked(),
                'fringes': float(self.fringes_edit.text()) if self.zernike_irregularity_check.isChecked() else 0.0
            },
            'tolerance_data': self.updated_tolerance_data
        }
        return settings

    # 添加新方法来检查TDE状态
    def check_and_set_button_state(self):
        """检查TDE是否有数据并设置按钮状态"""
        try:
            # 检查是否有选项被勾选
            has_checked_options = any([
                self.decenterX_check.isChecked(),
                self.decenterY_check.isChecked(),
                self.tiltX_check.isChecked(),
                self.tiltY_check.isChecked(),
                self.zernike_irregularity_check.isChecked()
            ])
            
            if not has_checked_options:
                self.param_edit_button.setEnabled(False)
                return
            
            TheSystem = self.parent().TheSystem
            tde = TheSystem.TDE
            tde.SetupMode = 1  # 设置为敏感性分析模式
            
            if tde and tde.NumberOfOperands > 0:
                self.param_edit_button.setEnabled(True)
            else:
                self.param_edit_button.setEnabled(False)
            
        except Exception:
            self.param_edit_button.setEnabled(False)

    # 修改OK按钮的处理方法
    def on_ok_clicked(self):
        """OK按钮点击处理"""
        # 直接接受对话框，因为公差操作数已经在Parameter Editing时设置好了
        self.accept()

class DataStructure:
    def __init__(self, operand_type, operand_value, operand_change):
        self.operand_type = operand_type
        self.operand_value = operand_value
        self.operand_change = operand_change

class SensitivityDataContainer:
    def __init__(self):
        self.data_list = []

    def append_data(self, operand_type, operand_value, operand_change):
        data = DataStructure(operand_type, operand_value, operand_change)
        self.data_list.append(data)

    def sort_data_ascending(self):
        self.data_list.sort(key=lambda x: x.operand_change)

    def sort_data_descending(self):
        self.data_list.sort(key=lambda x: x.operand_change, reverse=True)

    def clear_data(self):
        self.data_list = []