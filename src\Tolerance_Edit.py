from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QHeaderView, QComboBox, QMenu, QAction, QPushButton, QTableWidgetItem, QMessageBox

class ToleranceEditDialog(QDialog):
    # 定义一个信号，用于在确认按钮点击后发送数据
    data_updated = pyqtSignal(list)

    def __init__(self, current_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Tolerance Edit")
        self.layout = QVBoxLayout(self)
        
        # 初始化数据验证标志
        self.has_valid_data = False

        # 创建表格
        self.table_widget = QTableWidget(len(current_data), 8, self)
        # 初始设置默认表头
        self.default_headers = ['Type', 'Surf1', 'Surf2', '', 'Nominal', 'Min', 'Max', 'Comment']
        self.table_widget.setHorizontalHeaderLabels(self.default_headers)
        self.table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table_widget.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # 连接单元格变更信号
        self.table_widget.cellChanged.connect(self.validate_cell_data)
        self.table_widget.cellClicked.connect(self.on_cell_clicked)

        # 添加右键菜单
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.open_menu)

        self.layout.addWidget(self.table_widget)

        # 创建确认按钮
        self.confirm_button = QPushButton("Confirm")
        self.confirm_button.clicked.connect(self.on_confirm)
        self.confirm_button.setEnabled(False)  # 初始状态禁用
        self.layout.addWidget(self.confirm_button)

        # 填充现有数据
        self.populate_table(current_data)

    def validate_cell_data(self, row, column):
        """验证单元格数据"""
        try:
            if column in [4, 5, 6]:  # Nominal, Min, Max 列
                item = self.table_widget.item(row, column)
                if item and item.text().strip():
                    try:
                        float(item.text())
                    except ValueError:
                        item.setText("")
                        QMessageBox.warning(self, "输入错误", "请输入有效的数值")
            
            # 验证所有必需的数据
            self.validate_all_data()
            
        except Exception as e:
            print(f"验证数据时出错: {str(e)}")

    def validate_all_data(self):
        """验证所有数据的完整性"""
        try:
            valid = True
            for row in range(self.table_widget.rowCount()):
                combo_box = self.table_widget.cellWidget(row, 0)
                if combo_box is None:
                    print(f"行 {row}: Type列的ComboBox不存在")
                    valid = False
                    break
                    
                operand_type = combo_box.currentText()
                print(f"行 {row}: 操作数类型 = {operand_type}")
                
                # 根据不同的操作数类型进行验证
                if operand_type == "TWAV":
                    # TWAV类型只需要验证Wave值
                    wave_item = self.table_widget.item(row, 5)  # Wave在第6列
                    if not wave_item or not wave_item.text().strip():
                        print(f"行 {row}: Wave值为空")
                        valid = False
                        break
                elif operand_type == "COMP":
                    # COMP类型需要验证Surf和Code
                    surf_item = self.table_widget.item(row, 1)
                    code_item = self.table_widget.item(row, 2)
                    if not surf_item or not surf_item.text().strip() or not code_item or not code_item.text().strip():
                        print(f"行 {row}: Surf或Code值为空")
                        valid = False
                        break
                elif operand_type in ["TEXI", "TEZI"]:
                    # TEXI/TEZI类型需要验证Surf和Min/Max值
                    surf_item = self.table_widget.item(row, 1)
                    min_item = self.table_widget.item(row, 5)
                    max_item = self.table_widget.item(row, 6)
                    if not surf_item or not surf_item.text().strip():
                        print(f"行 {row}: Surf值为空")
                        valid = False
                        break
                    if not min_item or not max_item or not min_item.text().strip() or not max_item.text().strip():
                        print(f"行 {row}: Min或Max值为空")
                        valid = False
                        break
                else:  # TEDX, TEDY, TETX, TETY
                    # 偏心和倾斜类型需要验证Surf1, Surf2和Min/Max值
                    surf1_item = self.table_widget.item(row, 1)
                    surf2_item = self.table_widget.item(row, 2)
                    min_item = self.table_widget.item(row, 5)
                    max_item = self.table_widget.item(row, 6)
                    if not surf1_item or not surf1_item.text().strip() or not surf2_item or not surf2_item.text().strip():
                        print(f"行 {row}: Surf1或Surf2值为空")
                        valid = False
                        break
                    if not min_item or not max_item or not min_item.text().strip() or not max_item.text().strip():
                        print(f"行 {row}: Min或Max值为空")
                        valid = False
                        break
            
            self.has_valid_data = valid
            self.confirm_button.setEnabled(valid)
            print(f"数据验证结果: {'通过' if valid else '失败'}")
            
        except Exception as e:
            print(f"验证所有数据时出错: {str(e)}")
            self.has_valid_data = False
            self.confirm_button.setEnabled(False)

    def on_cell_clicked(self, row, column):
        """当单元格被点击时调用"""
        try:
            combo_box = self.table_widget.cellWidget(row, 0)
            if combo_box is not None:
                text = combo_box.currentText()
                if text == "COMP":
                    headers = ["Type", "Surf", "Code", "", "Nominal", "Min", "Max", "Comment"]
                elif text == "TWAV":
                    headers = ["Type", "", "", "", "Nominal", "Wave", "", "Comment"]
                elif text in ["TEXI", "TEZI"]:
                    headers = ["Type", "Surf", "Max#", "Min#", "Nominal", "Min", "Max", "Comment"]
                else:
                    headers = ["Type", "Surf1", "Surf2", "", "Nominal", "Min", "Max", "Comment"]
                self.table_widget.setHorizontalHeaderLabels(headers)
                
                # 更新表头后重新验证数据
                self.validate_all_data()
                
        except Exception as e:
            print(f"更新表头时出错: {str(e)}")

    def populate_table(self, current_data):
        """填充表格数据"""
        try:
            print("开始填充表格数据...")
            
            # 暂时断开cellChanged信号，避免重复验证
            self.table_widget.cellChanged.disconnect(self.validate_cell_data)
            
            self.table_widget.setRowCount(len(current_data))
            
            for row, data in enumerate(current_data):
                # 设置Type列
                type_combo = QComboBox()
                type_combo.addItems(["COMP", "TWAV", "TEDX", "TEDY", "TETX", "TETY", "TEXI", "TEZI"])
                current_type = data.get('Type', '')
                type_combo.setCurrentText(current_type)
                
                type_combo.currentTextChanged.connect(lambda text, r=row: self.on_cell_clicked(r, 0))
                self.table_widget.setCellWidget(row, 0, type_combo)
                
                # 根据操作数类型设置不同的数据
                self.set_row_data(row, data)
            
            # 重新连接cellChanged信号
            self.table_widget.cellChanged.connect(self.validate_cell_data)
            
            # 验证初始数据
            self.validate_all_data()
            
            print("表格数据填充完成")
            
        except Exception as e:
            import traceback
            print(f"填充表格时出错: {str(e)}")
            print(traceback.format_exc())

    def set_row_data(self, row, data):
        """设置行数据"""
        try:
            operand_type = data.get('Type', '')
            
            if operand_type == 'TWAV':
                for col in [1, 2, 3]:
                    self.table_widget.setItem(row, col, QTableWidgetItem(''))
                self.table_widget.setItem(row, 4, QTableWidgetItem(str(data.get('Nominal', ''))))
                self.table_widget.setItem(row, 5, QTableWidgetItem(str(data.get('Wave', ''))))
                self.table_widget.setItem(row, 6, QTableWidgetItem(''))
                
            elif operand_type == 'COMP':
                self.table_widget.setItem(row, 1, QTableWidgetItem(str(data.get('Surf', ''))))
                self.table_widget.setItem(row, 2, QTableWidgetItem(str(data.get('Code', ''))))
                self.table_widget.setItem(row, 3, QTableWidgetItem(''))
                self.table_widget.setItem(row, 4, QTableWidgetItem(str(data.get('Nominal', ''))))
                self.table_widget.setItem(row, 5, QTableWidgetItem(str(data.get('Min', ''))))
                self.table_widget.setItem(row, 6, QTableWidgetItem(str(data.get('Max', ''))))
                
            elif operand_type in ['TEDX', 'TEDY', 'TETX', 'TETY']:
                self.table_widget.setItem(row, 1, QTableWidgetItem(str(data.get('Surf1', ''))))
                self.table_widget.setItem(row, 2, QTableWidgetItem(str(data.get('Surf2', ''))))
                self.table_widget.setItem(row, 3, QTableWidgetItem(''))
                self.table_widget.setItem(row, 4, QTableWidgetItem(str(data.get('Nominal', ''))))
                self.table_widget.setItem(row, 5, QTableWidgetItem(str(data.get('Min', ''))))
                self.table_widget.setItem(row, 6, QTableWidgetItem(str(data.get('Max', ''))))
                
            elif operand_type in ['TEXI', 'TEZI']:
                self.table_widget.setItem(row, 1, QTableWidgetItem(str(data.get('Surf', ''))))
                self.table_widget.setItem(row, 2, QTableWidgetItem(str(data.get('Max#', ''))))
                self.table_widget.setItem(row, 3, QTableWidgetItem(str(data.get('Min#', ''))))
                self.table_widget.setItem(row, 4, QTableWidgetItem(str(data.get('Nominal', ''))))
                self.table_widget.setItem(row, 5, QTableWidgetItem(str(data.get('Min', ''))))
                self.table_widget.setItem(row, 6, QTableWidgetItem(str(data.get('Max', ''))))
            
            # 设置注释
            self.table_widget.setItem(row, 7, QTableWidgetItem(str(data.get('Comment', ''))))
            
        except Exception as e:
            print(f"设置行数据时出错: {str(e)}")

    def open_menu(self, position):
        menu = QMenu()
        add_action = QAction("Add Row", self)
        delete_action = QAction("Delete Row", self)
        menu.addAction(add_action)
        menu.addAction(delete_action)
        add_action.triggered.connect(self.add_row)
        delete_action.triggered.connect(self.delete_row)
        menu.exec_(self.table_widget.viewport().mapToGlobal(position))

    def add_row(self):
        """添加新行"""
        row_count = self.table_widget.rowCount()
        self.table_widget.insertRow(row_count)
        combo_box = QComboBox()
        combo_box.addItems(["COMP", "TWAV", "TEDX", "TEDY", "TETX", "TETY", "TEXI", "TEZI"])
        combo_box.currentTextChanged.connect(lambda text, r=row_count: self.on_cell_clicked(r, 0))
        self.table_widget.setCellWidget(row_count, 0, combo_box)
        self.validate_all_data()

    def delete_row(self):
        selected_row = self.table_widget.currentRow()
        if selected_row >= 0:
            self.table_widget.removeRow(selected_row)
            self.validate_all_data()

    def read_table_data(self):
        """读取修改完成后的表格内容"""
        table_data = []
        try:
            for row in range(self.table_widget.rowCount()):
                combo_box = self.table_widget.cellWidget(row, 0)
                if combo_box is not None:
                    text = combo_box.currentText()
                    row_data = self.get_row_data(row, text)
                    if row_data:
                        table_data.append(row_data)
        except Exception as e:
            print(f"读取表格数据时出错: {str(e)}")
        return table_data

    def get_row_data(self, row, operand_type):
        """获取行数据"""
        try:
            if operand_type == "COMP":
                return {
                    'Type': operand_type,
                    'Surf': self.get_cell_text(row, 1),
                    'Code': self.get_cell_text(row, 2),
                    'Nominal': self.get_cell_text(row, 4),
                    'Min': self.get_cell_text(row, 5),
                    'Max': self.get_cell_text(row, 6),
                    'Comment': self.get_cell_text(row, 7)
                }
            elif operand_type == "TWAV":
                return {
                    'Type': operand_type,
                    'Nominal': self.get_cell_text(row, 4),
                    'Wave': self.get_cell_text(row, 5),
                    'Comment': self.get_cell_text(row, 7)
                }
            elif operand_type in ["TEXI", "TEZI"]:
                return {
                    'Type': operand_type,
                    'Surf': self.get_cell_text(row, 1),
                    'Max#': self.get_cell_text(row, 2),
                    'Min#': self.get_cell_text(row, 3),
                    'Nominal': self.get_cell_text(row, 4),
                    'Min': self.get_cell_text(row, 5),
                    'Max': self.get_cell_text(row, 6),
                    'Comment': self.get_cell_text(row, 7)
                }
            else:
                return {
                    'Type': operand_type,
                    'Surf1': self.get_cell_text(row, 1),
                    'Surf2': self.get_cell_text(row, 2),
                    'Nominal': self.get_cell_text(row, 4),
                    'Min': self.get_cell_text(row, 5),
                    'Max': self.get_cell_text(row, 6),
                    'Comment': self.get_cell_text(row, 7)
                }
        except Exception as e:
            print(f"获取行数据时出错: {str(e)}")
            return None

    def get_cell_text(self, row, col):
        """获取单元格文本，处理None情况"""
        item = self.table_widget.item(row, col)
        return item.text() if item else ''

    def on_confirm(self):
        """确认按钮点击事件处理函数"""
        if not self.has_valid_data:
            QMessageBox.warning(self, "验证失败", "请确保所有必需的数据都已正确填写")
            return
            
        try:
            # 获取表格数据
            table_data = self.read_table_data()
            if not table_data:
                QMessageBox.warning(self, "数据错误", "无法读取表格数据")
                return
            
            # 获取TDE对象
            TheSystem = self.parent().parent().TheSystem
            tde = TheSystem.TDE
            
            # 操作数类型映射字典（数字到字符串的映射）
            operand_type_map = {
                32: 'COMP',  # 补偿器
                37: 'TWAV',  # 测试波长
                17: 'TEDX',  # X偏心
                18: 'TEDY',  # Y偏心
                20: 'TETX',  # X倾斜
                21: 'TETY',  # Y倾斜
                14: 'TEXI',  # 不规则性X
                41: 'TEZI',  # 不规则性Z
            }
            
            # 字符串到数字的反向映射
            reverse_type_map = {v: k for k, v in operand_type_map.items()}
            
            # 获取当前操作树中的操作数
            current_operands = []
            for i in range(1, tde.NumberOfOperands + 1):
                current_operand = tde.GetOperandAt(i)
                if current_operand:
                    current_operands.append({
                        'index': i,
                        'type': operand_type_map.get(current_operand.Type, ''),
                        'operand': current_operand
                    })
            
            print(f"当前操作树中有 {len(current_operands)} 个操作数")
            
            # 处理每个表格数据
            for i, new_data in enumerate(table_data, start=1):
                new_type = new_data['Type']
                
                # 如果索引在现有操作数范围内
                if i <= len(current_operands):
                    current_op = current_operands[i-1]
                    
                    # 如果类型相同，检查并更新参数
                    if current_op['type'] == new_type:
                        print(f"更新第 {i} 个操作数参数")
                        self.update_operand_params(current_op['operand'], new_data)
                    else:
                        # 类型不同，删除原操作数并创建新的
                        print(f"替换第 {i} 个操作数，从 {current_op['type']} 到 {new_type}")
                        tde.RemoveOperandsAt(i, 1)
                        new_operand = tde.InsertNewOperandAt(i)
                        # 使用ChangeType方法设置操作数类型
                        new_operand.ChangeType(reverse_type_map[new_type])
                        self.update_operand_params(new_operand, new_data)
                else:
                    # 添加新的操作数
                    print(f"添加新操作数 {new_type} 在位置 {i}")
                    new_operand = tde.InsertNewOperandAt(i)
                    # 使用ChangeType方法设置操作数类型
                    new_operand.ChangeType(reverse_type_map[new_type])
                    self.update_operand_params(new_operand, new_data)
            
            # 如果表格数据少于当前操作数，删除多余的操作数
            if len(table_data) < len(current_operands):
                extra_count = len(current_operands) - len(table_data)
                print(f"删除 {extra_count} 个多余的操作数")
                tde.RemoveOperandsAt(len(table_data) + 1, extra_count)
            
            print("操作数更新完成")
            
            # 发送数据更新信号
            self.data_updated.emit(table_data)
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存数据时出错：\n{str(e)}")

    def update_operand_params(self, operand, data):
        """更新操作数的参数"""
        try:
            operand_type = data['Type']
            
            if operand_type == 'TWAV':
                if 'Wave' in data:
                    operand.Min = float(data['Wave'])
            elif operand_type == 'COMP':
                if 'Surf' in data:
                    operand.Param1 = int(data['Surf'])
                if 'Code' in data:
                    operand.Param2 = int(data['Code'])
                if 'Min' in data and data['Min']:
                    operand.Min = float(data['Min'])
                if 'Max' in data and data['Max']:
                    operand.Max = float(data['Max'])
            elif operand_type in ['TEDX', 'TEDY', 'TETX', 'TETY']:
                if 'Surf1' in data:
                    operand.Param1 = int(data['Surf1'])
                if 'Surf2' in data:
                    operand.Param2 = int(data['Surf2'])
                if 'Min' in data and data['Min']:
                    operand.Min = float(data['Min'])
                if 'Max' in data and data['Max']:
                    operand.Max = float(data['Max'])
            elif operand_type in ['TEXI', 'TEZI']:
                if 'Surf' in data:
                    operand.Param1 = int(data['Surf'])
                if 'Max#' in data:
                    operand.Param2 = int(data['Max#'])
                if 'Min#' in data:
                    operand.Param3 = int(data['Min#'])
                if 'Min' in data and data['Min']:
                    operand.Min = float(data['Min'])
                if 'Max' in data and data['Max']:
                    operand.Max = float(data['Max'])
            
            # 设置注释
            if 'Comment' in data:
                operand.Comment = str(data['Comment'])
                
        except Exception as e:
            print(f"更新操作数参数时出错: {str(e)}")
