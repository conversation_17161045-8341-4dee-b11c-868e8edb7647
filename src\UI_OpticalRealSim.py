import os

from PyQt5 import QtCore, QtGui
from PyQt5.QtWidgets import  QFileDialog, QApplication, QPushButton, QWidget, QVBoxLayout, QHBoxLayout, QCheckBox
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

import sys
import time
import matplotlib
import numpy as np
import clr
from System import Enum, Int32, Double
from win32comext.shell.demos.explorer_browser import MainWindow

from Prediction import AddVariableDialog
from Prediction import OptimizationItem
from Prediction import PredictionVariable
from Sensitivity import SensitivityDataContainer

from qLog import LogDisplay
from qLog import LogFramework
from Sensitivity import SensivitityDialog
from Sensitivity import SensitivityDataContainer
from Sensitivity import *

matplotlib.use("Qt5Agg")
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt

# 自定义绘图插件
from PlotDataWidget import PlotDataWidget

# UI
import ui.OpticalRealSim
# [BUG] 使用分裂期splitter在操作plt控件时，当控件大小为0时，程序闪退
# [TODO] 将控件改为stacked widget
#import OpticalRealSim

# zemax
from PythonZemaxApp import PythonStandaloneApplication

# 导入部分添加OpticalSystemWidget
from PyQt5 import QtCore, QtGui
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QMessageBox, QTabWidget, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QLineEdit, QComboBox, QGridLayout, QGroupBox, QCheckBox, QSplitter, QFrame
import sys
import os
import copy
import numpy as np
import logging
import math
import subprocess
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from mpl_toolkits.mplot3d import Axes3D
from utils import save_dict_to_json
from PlotDataWidget import PlotDataWidget
from OpticalSystemWidget import OpticalSystemWidget  # 新增导入

class UI_OpticalRealSim(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 初始化UI组件
        self.initUI()
        
        # 初始化数据
        self.init_data()
        
        # 连接信号槽
        self.connectSignalSlot()

    def initUI(self):
        super(UI_OpticalRealSim, self).__init__()
        self.showMaximized()
        self.zemax_filename=''
        #
        self.ui = ui.OpticalRealSim.Ui_MainWindow()
        self.ui.setupUi(self)
        # 移除这行，避免重复连接open_zemax信号
        # self.ui.pushButtonOpenZemax.clicked.connect(self.open_zemax)
        
        # 获取tabWidget
        self.tabWidget = self.ui.tabWidget
        
        self.init_parameters()
        self.init_zemax()
        # 设置表头
        self.ui.tableWidgetRealData.setColumnCount(39)
        self.ui.tableWidgetRealData.setHorizontalHeaderLabels(["Name"] + ["Radius"] + [str(i) for i in range(1, 38)])
        # 设置日志
        self.log_display = LogDisplay(self.ui.textBrowserLog)
        self.log_framework = LogFramework(self.log_display)
        self.log_framework.info("Optical Analysis Software based on Measured data.")
        # 预测优化
        self.prediction_variables = []      # 存储所有变量结构体，包括: 镜面ID、变量类型
        self.prediction_last_values = []    #
        self.prediction_round = 0           # 预测次数，用于记录
        self.str_txt = ''
        self.ui.tableWidgetPedictionVariables.setColumnCount(2)
        self.ui.tableWidgetPedictionVariables.setHorizontalHeaderLabels(["Mirror ID", "Optimization Item"])
        self.ui.radioButtonPredictionRMS.clicked.connect(self.onPredictionRadioChanged)
        self.ui.radioButtonPredictionSpot.clicked.connect(self.onPredictionRadioChanged)
        self.ui.radioButtonPredictionCustom.clicked.connect(self.onPredictionRadioChanged)
        
        # 添加显示所有视场的复选框
        self.ui.checkBoxShowAllFields = QCheckBox("显示所有视场")
        self.ui.checkBoxShowAllFields.setChecked(False)
        # 将复选框添加到正确的位置 - tabSpotDiagram和gridLayout_2
        if hasattr(self.ui, 'tabSpotDiagram') and hasattr(self.ui, 'gridLayout_2'):
            self.ui.gridLayout_2.addWidget(self.ui.checkBoxShowAllFields, 2, 0, 1, 2)
        else:
            # 备选方案：尝试通过查找控件获取
            tabSpotDiagram = self.findChild(QWidget, "tabSpotDiagram")
            if tabSpotDiagram:
                gridLayout = tabSpotDiagram.findChild(QGridLayout, "gridLayout_2")
                if gridLayout:
                    gridLayout.addWidget(self.ui.checkBoxShowAllFields, 2, 0, 1, 2)
                else:
                    # 最后的备选方案：直接添加到主窗口
                    self.ui.verticalLayout_3.addWidget(self.ui.checkBoxShowAllFields)
            else:
                # 如果都找不到，添加到主窗口某个可见的布局
                self.layout().addWidget(self.ui.checkBoxShowAllFields)
        
        # 连接信号
        self.ui.checkBoxShowAllFields.stateChanged.connect(self.update_spot)

        self.updateUIStatus(False)

        # 添加这一行: 连接标签页切换信号
        self.ui.tabWidget.currentChanged.connect(self.on_tab_changed)
        
        # 替换为: 在已有的tabInspection中添加光学系统视图
        self.optical_system_widget = OpticalSystemWidget()
        if hasattr(self.ui, 'tabInspection'):
            inspection_layout = QVBoxLayout(self.ui.tabInspection)
            inspection_layout.addWidget(self.optical_system_widget)
            self.log_framework.info("已在tabInspection中添加光学系统视图")
        else:
            # 尝试通过查找控件获取
            tab_inspection = self.findChild(QWidget, "tabInspection")
            if tab_inspection:
                inspection_layout = QVBoxLayout(tab_inspection)
                inspection_layout.addWidget(self.optical_system_widget)
                self.log_framework.info("已通过findChild找到tabInspection并添加光学系统视图")
            else:
                self.log_framework.error("未找到tabInspection标签页")

    # 初始化数据
    def init_data(self):
        """初始化数据变量"""
        self.lens_data = []
        self.system_data = []
        self.fields_num = 0
        self.wavelengths_num = 0
        self.nsur = 0
        
    # 连接信号槽
    def connectSignalSlot(self):
        """连接信号和槽"""
        self.ui.pushButtonOpenZemax.clicked.connect(self.open_zemax)
        # 添加Assembly Tolerance模块的Load按钮信号连接
        self.ui.pushButtonLoadTolerance.clicked.connect(self.load_tolerance_file)
        # Check按钮连接到check_assembly_tolerance方法
        self.ui.pushButtonCheck.clicked.connect(self.check_assembly_tolerance)
        # Analysis按钮连接到analysis_assembly_tolerance方法
        self.ui.pushButtonAnalysis.clicked.connect(self.analysis_assembly_tolerance)
        # 添加敏感性分析按钮的信号连接（在此处统一连接，避免重复）
        self.ui.pushButtonSensitivityConfig.clicked.connect(self.sensitivityConfig)
        self.ui.pushButtonSensitivityAnalysis.clicked.connect(self.sensitivityAnalysis)
        pass
        
    # 初始化绘制参数
    def init_parameters(self):
        self.sampling_items = ['32x32', '64x64', '128x128', '256x256', '512x512']
        self.rotation_items = ['0', '90', '180', '270']
        # 初始化点列图控件
        self.spot_widget = PlotDataWidget()
        w = self.ui.widget
        if w:
            w_layout = w.layout()
            if w_layout is None:
                w_layout = QVBoxLayout(w)
                w.setLayout(w_layout)
            w_layout.addWidget(self.spot_widget)

        # 初始化波前绘制控件
        self.wavefront_map_widget = PlotDataWidget()
        frame = self.ui.frame
        if frame:
            frame_layout = frame.layout()
            if frame_layout is None:
                frame_layout = QVBoxLayout(frame)
                frame.setLayout(frame_layout)
            frame_layout.addWidget(self.wavefront_map_widget)

        # 初始化MTF
        self.mtf_widget = PlotDataWidget()
        mtf_frame = self.ui.widgetMTF
        if mtf_frame:
            mtf_layout = mtf_frame.layout()
            if mtf_layout is None:
                mtf_layout = QVBoxLayout(mtf_frame)
                mtf_frame.setLayout(mtf_layout)
            mtf_layout.addWidget(self.mtf_widget)

        # 初始化PSF
        self.psf_widget = PlotDataWidget()
        psf_frame = self.ui.widgetPSF
        if psf_frame:
            psf_layout = psf_frame.layout()
            if psf_layout is None:
                psf_layout = QVBoxLayout(psf_frame)
                psf_frame.setLayout(psf_layout)
            psf_layout.addWidget(self.psf_widget)

    # 初始化zemax API
    def init_zemax(self):
        # init ZOS python 初始化ZOS
        self.zos = PythonStandaloneApplication()
        self.ZOSAPI = self.zos.ZOSAPI
        self.TheApplication = self.zos.TheApplication
        self.TheSystem = self.zos.TheSystem

        # test = self.TheSystem.Tools.OpenLocalOptimization()
        # print('open opt:', test)
        # test.Close()

        # Set up Batch Ray Trace
        #self.raytrace = self.TheSystem.Tools.OpenBatchRayTrace()
        #print('raytrace', self.raytrace)
        # self.LocalOpt = self.zos.TheSystem.Tools.OpenGlobalOptimization()
        # print('opt', self.LocalOpt)

    # 打开zemax文件
    def open_zemax(self):
        """
        打开文件选择对话框，过滤指定后缀的文件，并返回选择的文件名（如果有选择）
        """
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_filter = "zemax file (*.zmx);;zemax file (*.zmx)"  # 根据需求指定文件后缀过滤条件
        file_name, _ = QFileDialog.getOpenFileName(self, "select file", "", file_filter, options=options)
        # 如果读取到了zemax文件，执行操作
        if file_name:
            self.updateUIStatus(True)
            self.log_framework.debug("file name {0}".format(file_name))
            self.zemax_filename = file_name
            self.ui.lineEditZemaxFilename.setText(self.zemax_filename)
            self.TheSystem.LoadFile(self.zemax_filename, False)

            self.display_system_data()
            self.display_lens_data()
            
            # 增加: 显式调用更新光学系统视图
            self.update_optical_system_view()
            
            # 切换到Inspection标签页以便查看光学系统视图
            inspection_index = -1
            for i in range(self.ui.tabWidget.count()):
                if "Inspection" in self.ui.tabWidget.tabText(i):
                    inspection_index = i
                    break
            
            if inspection_index >= 0:
                self.ui.tabWidget.setCurrentIndex(inspection_index)
                self.log_framework.info(f"已切换到Inspection标签页（索引 {inspection_index}）")
            
            # 图表初次显示，参数配置好(指定默认参数)，显示控件按照默认参数进行绘制

            ## spot
            self.ui.spinBoxSpotRayDensity.setValue(20)  # 将初始值从30改为6
            self.ui.spinBoxSpotRayDensity.setMinimum(10)  # 设置最小值为3
            self.ui.spinBoxSpotRayDensity.setMaximum(30)
            self.ui.comboBoxSpotReferTo.addItems(['Chief Ray', 'Centroid', 'Middle', 'Vertex'])
            self.ui.comboBoxSpotReferTo.setCurrentIndex(0)  # 默认使用Chief Ray方法
            self.ui.comboBoxSpotWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxSpotWavelength.addItem(str(n+1))
                pass
            for n in range(self.fields_num):
                self.ui.comboBoxSpotField.addItem(str(n + 1))
                pass

            ## wavefronmap，打开zemax文件后，进行参数设置
            for item in self.sampling_items:
                self.ui.comboBoxWavefrontMapSampling.addItem(item)
            for item in self.rotation_items:
                self.ui.comboBoxWavefrontMapRotation.addItem(item)
            for n in range(self.wavelengths_num):
                self.ui.comboBoxWavefrontMapWavelength.addItem(str(n+1))
                pass
            for n in range(self.fields_num):
                self.ui.comboBoxWavefrontMapField.addItem(str(n + 1))
                pass

            ### MTF参数设置
            self.ui.spinBoxMTFFrequency.setValue(50)
            self.ui.spinBoxMTFFrequency.setMinimum(10)
            self.ui.spinBoxMTFFrequency.setMaximum(150)
            for item in self.sampling_items:
                self.ui.comboBoxMTFSampling.addItem(item)
            self.ui.comboBoxMTFWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxMTFWavelength.addItem(str(n + 1))
            self.ui.comboBoxMTFField.addItem('All')
            for n in range(self.fields_num):
                self.ui.comboBoxMTFField.addItem(str(n + 1))

            ### PSF参数设置
            for item in self.sampling_items:
                self.ui.comboBoxPSFSampling.addItem(item)
            self.ui.comboBoxPSFRotation.addItems(['CW0', 'CW90', 'CW180', 'CW270'])
            self.ui.comboBoxPSFWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxPSFWavelength.addItem(str(n + 1))
            for n in range(self.fields_num):
                self.ui.comboBoxPSFField.addItem(str(n + 1))

            # 计算和绘制点列图
            self.update_spot()
            self.ui.spinBoxSpotRayDensity.editingFinished.connect(self.update_spot)
            self.ui.comboBoxSpotReferTo.currentIndexChanged.connect(self.update_spot)
            self.ui.comboBoxSpotWavelength.currentIndexChanged.connect(self.update_spot)
            self.ui.comboBoxSpotField.currentIndexChanged.connect(self.update_spot)

            # 计算和绘制波前
            self.update_wavefront_map()
            self.ui.comboBoxWavefrontMapSampling.currentIndexChanged.connect(self.update_wavefront_map)
            self.ui.comboBoxWavefrontMapWavelength.currentIndexChanged.connect(self.update_wavefront_map)
            self.ui.comboBoxWavefrontMapField.currentIndexChanged.connect(self.update_wavefront_map)

            # 计算MTF
            self.update_MTF()
            self.ui.spinBoxMTFFrequency.editingFinished.connect(self.update_MTF)
            self.ui.comboBoxMTFSampling.currentIndexChanged.connect(self.update_MTF)
            self.ui.comboBoxMTFWavelength.currentIndexChanged.connect(self.update_MTF)
            self.ui.comboBoxMTFField.currentIndexChanged.connect(self.update_MTF)

            # 计算PSF
            self.update_PSF()
            self.ui.comboBoxPSFSampling.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFRotation.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFWavelength.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFField.currentIndexChanged.connect(self.update_PSF)


            # 设置信号槽函数，打开zemax后才能允许读取实测数据
            self.ui.pushButtonAdd.clicked.connect(self.addRealZernike)
            self.ui.pushButtonDelete.clicked.connect(self.delRealZernike)
            self.ui.pushButtonUpdate.clicked.connect(self.updateRealZernike)

            # 预测优化
            self.ui.pushButtonPrediction.clicked.connect(self.systemPrediction)
            self.ui.pushButtonAddPredictionVariable.clicked.connect(self.addPredictionVariable)
            self.ui.pushButtonDeletePredictionVariable.clicked.connect(self.deletePredictionVariable)
            self.ui.pushButtonDeleteAllPredictionVariable.clicked.connect(self.deleteAllPredictionVariable)
            self.ui.pushButtonLoadPredictionConfig.clicked.connect(self.loadPredictionConfigFile)
        else:
            print("未选择任何文件")
            return None

    # 更新UI启用和禁止
    def updateUIStatus(self, b):

      self.ui.tabWidget.setEnabled(b)
      self.ui.tabWidget_2.setEnabled(b)
      self.ui.groupBox.setEnabled(b)
      pass

    # 显示系统数据
    def display_system_data(self):
        """
        获取self.TheSystem.SystemData中的系统数据
        self.system_data, key-value, n行2列，第一列是名称，第二列对应值
        """
        self.system_data=[]
        self.system_data.append({"ApertureType": self.TheSystem.SystemData.Aperture.ApertureType})
        self.system_data.append({"ApertureValue": self.TheSystem.SystemData.Aperture.ApertureValue})
        self.fields_num = self.TheSystem.SystemData.Fields.NumberOfFields
        self.wavelengths_num = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
        self.system_data.append({"FieldsNum": self.fields_num})
        self.system_data.append({"WavelengthsNum": self.wavelengths_num})

        self.log_framework.info("fields number: {0}".format(self.fields_num))
        # 遍历视场
        for n in range(self.fields_num):
            field_str = "[X:{0}, Y:{1}, W:{2}, active:{3}]".format(self.TheSystem.SystemData.Fields.GetField(n + 1).X, self.TheSystem.SystemData.Fields.GetField(n + 1).Y, self.TheSystem.SystemData.Fields.GetField(n + 1).Weight, self.TheSystem.SystemData.Fields.GetField(n + 1).IsActive)
            self.system_data.append({"Field" + str(n + 1): field_str})
            pass

        self.log_framework.info("wavelengths number: {0}".format(self.wavelengths_num))
        # 遍历波长
        for n in range(self.wavelengths_num):
            wave_str = "[wavelength:{0}, W:{1}, active:{2}, primary:{3}]".format(self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).Wavelength, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).Weight, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).IsActive, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).IsPrimary)
            self.system_data.append({"Wavelength" + str(n + 1): wave_str})
            pass

        self.system_data.append({"AdjustIndexDataToEnvironment": self.TheSystem.SystemData.Environment.AdjustIndexToEnvironment})
        self.system_data.append({"Temperature": self.TheSystem.SystemData.Environment.Temperature})
        self.system_data.append({"Pressure": self.TheSystem.SystemData.Environment.Pressure})

        # [TODO] 添加其他信息
        self.system_data.append({"LensUnits(0:mm,1:cm,2:inc,3:m)": self.TheSystem.SystemData.Units.LensUnits})
        self.system_data.append({"SourceUnits(0:watts,1:lumens,2:joules)": self.TheSystem.SystemData.Units.SourceUnits})

        self.ui.tableWidgetSystemData.setRowCount(len(self.system_data))
        # 总共两列
        self.ui.tableWidgetSystemData.setColumnCount(2)
        # 第二列设置自动宽度
        self.ui.tableWidgetSystemData.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        # 将所有self.system_data数据进行显示
        for row in range(len(self.system_data)):
            key = list(self.system_data[row].keys())[0]
            value = self.system_data[row][key]
            item_key = QTableWidgetItem(str(key))
            item_value = QTableWidgetItem(str(value))
            self.ui.tableWidgetSystemData.setItem(row, 0, item_key)
            self.ui.tableWidgetSystemData.setItem(row, 1, item_value)

    # 显示镜头数据
    def display_lens_data(self):
        # list容器，每一个镜头数据组成一个list
        self.lens_data = []
        self.nsur = self.TheSystem.LDE.NumberOfSurfaces
        
        # 定义固定的通用属性列
        fixed_columns = [
            {'name': 'ID', 'attr': None},  # ID是特殊的，不是属性
            {'name': 'Surface Type', 'attr': 'TypeName'},
            {'name': 'Comment', 'attr': 'Comment'},
            {'name': 'Radius', 'attr': 'Radius'},
            {'name': 'Thickness', 'attr': 'Thickness'},
            {'name': 'Material', 'attr': 'Material'},
            {'name': 'Clear Semi-Diameter', 'attr': 'SemiDiameter'},
            {'name': 'Mech Semi-Diameter', 'attr': 'MechanicalSemiDiameter'},
            {'name': 'Conic', 'attr': 'Conic'},
            {'name': 'TCE', 'attr': 'TCE'}  # 添加TCE属性
        ]
        
        # 定义不同表面类型的Par参数描述
        par_descriptions = {
            'Coordinate Break': {
                'Par1': 'Decenter X',
                'Par2': 'Decenter Y',
                'Par3': 'Tilt X',
                'Par4': 'Tilt Y', 
                'Par5': 'Tilt Z',
                'Par6': 'Order'
            },
            
            'Standard': {
                # 标准表面的Par参数描述
            }
        }
        
        # 默认描述，确保常见表面类型的参数描述都可用
        default_descriptions = {
            'Par1': 'Par 1',
            'Par2': 'Par 2',
            'Par3': 'Par 3',
            'Par4': 'Par 4',
            'Par5': 'Par 5',
            'Par6': 'Par 6',
            'Par7': 'Par 7',
            'Par8': 'Par 8',
            'Par9': 'Par 9',
            'Par10': 'Par 10',
            'Par11': 'Par 11',
            'Par12': 'Par 12',
            'Par13': 'Par 13',
            'Par14': 'Par 14'
        }
        
        # 每个镜面的Par参数标签存储 - 类成员变量，供后续单元格点击事件使用
        self.par_labels_by_surface = {}
        
        # 收集所有镜面的参数
        for n in range(self.nsur):
            lens_data = {}
            surface = self.TheSystem.LDE.GetSurfaceAt(n)
            
            # 添加ID（索引值）
            lens_data['ID'] = str(n)
            
            # 获取镜面类型
            surface_type = surface.TypeName if hasattr(surface, 'TypeName') else "Unknown"
            self.log_framework.debug(f"处理镜面 ID: {n}, 类型: {surface_type}")
            
            # 添加其他通用属性
            for col in fixed_columns[1:]:  # 跳过ID
                if col['attr'] and hasattr(surface, col['attr']):
                    try:
                        value = getattr(surface, col['attr'])
                        lens_data[col['name']] = value
                    except Exception as e:
                        self.log_framework.debug(f"获取属性 {col['attr']} 时出错: {str(e)}")
                        lens_data[col['name']] = ""
                else:
                    lens_data[col['name']] = ""
            
            # 尝试获取表面单元格值 (Par系列)
            try:
                surface_par_labels = {}  # 存储当前镜面的Par参数标签
                
                for i in range(1, 15):  # 通常Par1到Par14
                    try:
                        par_column = getattr(self.ZOSAPI.Editors.LDE.SurfaceColumn, f'Par{i}')
                        cell = surface.GetSurfaceCell(par_column)
                        if cell and hasattr(cell, 'Value') and cell.Value:
                            lens_data[f'Par{i}'] = cell.Value
                            
                            # 尝试多种方式获取参数名称
                            parameter_label = None
                            
                            # 1. 首先尝试从cell直接获取Label
                            try:
                                if hasattr(cell, 'Label') and cell.Label and cell.Label.strip():
                                    parameter_label = cell.Label.strip()
                            except Exception as e:
                                self.log_framework.debug(f"从cell.Label获取参数标签失败: {str(e)}")
                            
                            # 2. 尝试从cell的Name或Description获取
                            if not parameter_label:
                                try:
                                    if hasattr(cell, 'Name') and cell.Name and cell.Name.strip():
                                        parameter_label = cell.Name.strip()
                                    elif hasattr(cell, 'Description') and cell.Description and cell.Description.strip():
                                        parameter_label = cell.Description.strip()
                                except Exception as e:
                                    self.log_framework.debug(f"从cell的Name/Description获取参数标签失败: {str(e)}")
                            
                            # 3. 使用预设的描述
                            if not parameter_label:
                                # 检查是否有此表面类型的特定描述
                                if surface_type in par_descriptions and f'Par{i}' in par_descriptions[surface_type]:
                                    parameter_label = par_descriptions[surface_type][f'Par{i}']
                                # 如果没有特定描述，使用默认描述
                                elif f'Par{i}' in default_descriptions:
                                    parameter_label = default_descriptions[f'Par{i}']
                            
                            # 将参数标签存储到当前镜面的字典中
                            if parameter_label:
                                surface_par_labels[f'Par{i}'] = parameter_label
                                self.log_framework.debug(f"镜面 {n} 的 Par{i} 参数标签: {parameter_label}")
                            else:
                                self.log_framework.debug(f"镜面 {n} 的 Par{i} 参数标签为空")
                    except Exception as e:
                        self.log_framework.debug(f"获取镜面 {n} 的 Par{i} 参数和标签时出错: {str(e)}")
                
                # 存储当前镜面的Par参数标签
                if surface_par_labels:
                    self.log_framework.debug(f"镜面 {n} 有 {len(surface_par_labels)} 个Par参数标签")
                    self.par_labels_by_surface[n] = surface_par_labels
                else:
                    self.log_framework.debug(f"镜面 {n} 没有Par参数标签")
                
            except Exception as e:
                self.log_framework.debug(f"获取镜面 {n} 的Par参数时出错: {str(e)}")
            
            self.lens_data.append(lens_data)
        
        # 找出所有出现的列
        all_columns = set()
        for data in self.lens_data:
            all_columns.update(data.keys())
        
        # 构建有序的列列表：先是固定的通用属性，然后是其他属性
        fixed_names = [col['name'] for col in fixed_columns]
        
        # 将Par参数按照数字顺序而非字典序排序
        other_columns = [col for col in all_columns if col not in fixed_names]
        par_columns = []
        non_par_columns = []
        
        # 分离Par参数和其他参数
        for col in other_columns:
            if col.startswith('Par') and col[3:].isdigit():
                par_columns.append(col)
            else:
                non_par_columns.append(col)
        
        # 对Par参数按照数字进行排序
        par_columns.sort(key=lambda x: int(x[3:]))
        
        # 其他参数按照字母顺序排序
        non_par_columns.sort()
        
        # 合并所有列
        columns = fixed_names + par_columns + non_par_columns
        
        # 存储类成员变量，供后续单元格点击事件使用
        self.fixed_names = fixed_names
        self.par_columns = par_columns
        self.non_par_columns = non_par_columns
        self.all_lens_columns = columns
        
        # 创建表格
        self.ui.tableWidgetLensData.setRowCount(len(self.lens_data))
        self.ui.tableWidgetLensData.setColumnCount(len(columns))
        
        # 设置默认表头标签
        header_labels = []
        for col in fixed_names:
            header_labels.append(col)
        
        # 为Par参数设置默认描述性表头
        for col in par_columns:
            # 查找第一个有此参数标签的镜面
            param_label = None
            for surface_id, labels in self.par_labels_by_surface.items():
                if col in labels:
                    param_label = labels[col]
                    self.log_framework.debug(f"为 {col} 设置表头标签: {param_label} (来自镜面 {surface_id})")
                    break
            
            if param_label:
                header_labels.append(f"{col}\n({param_label})")
            else:
                header_labels.append(col)
                self.log_framework.debug(f"未找到 {col} 的参数标签")
        
        # 添加其他列的标题
        for col in non_par_columns:
            header_labels.append(col)
        
        self.ui.tableWidgetLensData.setHorizontalHeaderLabels(header_labels)
        
        # 填充数据
        for row, data in enumerate(self.lens_data):
            for col, column_name in enumerate(columns):
                # 设置单元格值
                value = data.get(column_name, "")
                item = QTableWidgetItem(str(value))
                self.ui.tableWidgetLensData.setItem(row, col, item)
        
        # 自动调整行高和列宽
        self.ui.tableWidgetLensData.resizeRowsToContents()
        self.ui.tableWidgetLensData.resizeColumnsToContents()
        
        # 设置所有列的宽度模式为Interactive（用户可以调整）
        for i in range(len(columns)):
            self.ui.tableWidgetLensData.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)
        
        # 连接单元格点击事件，更新表头
        self.ui.tableWidgetLensData.cellClicked.connect(self.update_header_for_selected_surface)

        # 在显示镜片数据后更新光学系统视图
        self.update_optical_system_view()

    # 新增方法：当用户点击表格中的单元格时，根据所在行更新表头
    def update_header_for_selected_surface(self, row, column):
        # 获取选中行的镜面ID
        id_item = self.ui.tableWidgetLensData.item(row, 0)
        if not id_item:
            return
        
        surface_id = int(id_item.text())
        surface_type_item = self.ui.tableWidgetLensData.item(row, 1)  # Surface Type列
        surface_type = surface_type_item.text() if surface_type_item else "Unknown"
        
        # 准备更新表头标签
        header_labels = []
        
        # 固定列保持原样
        for col in self.fixed_names:
            header_labels.append(col)
        
        # 根据选中的镜面，更新Par参数列的标题
        for col in self.par_columns:
            # 检查选中的镜面是否有此Par参数的标签
            if surface_id in self.par_labels_by_surface and col in self.par_labels_by_surface[surface_id]:
                # 获取该镜面的Par参数标签
                parameter_label = self.par_labels_by_surface[surface_id][col]
                header_text = f"{col}\n({parameter_label})"
            else:
                header_text = col
            header_labels.append(header_text)
        
        # 其他列保持原样
        for col in self.non_par_columns:
            header_labels.append(col)
        
        # 更新表头，并显示当前正在查看哪个镜面的参数信息
        self.ui.tableWidgetLensData.setHorizontalHeaderLabels(header_labels)
        self.log_framework.info(f"正在显示镜面ID {surface_id} ({surface_type}) 的参数信息")
        
        # 突出显示选中的行
        self.ui.tableWidgetLensData.selectRow(row)

    # 添加一个辅助方法来安全地获取属性
    def safe_add_attribute(self, lens_dict, obj, attribute_name, display_name=None):
        """安全地获取对象属性并添加到字典中
        
        Args:
            lens_dict: 存储属性的字典
            obj: 要获取属性的对象
            attribute_name: 属性名称
            display_name: 显示名称（可选）
            
        Returns:
            bool: 是否成功添加属性
        """
        if not display_name:
            display_name = attribute_name
        
        try:
            if hasattr(obj, attribute_name):
                value = getattr(obj, attribute_name)
                # 对于方法，忽略
                if not callable(value):
                    lens_dict[display_name] = value
                    return True
        except Exception as e:
            self.log_framework.debug(f"获取属性 {attribute_name} 时出错: {str(e)}")
        return False

    # 计算和绘制点列图 EXAMPLE22
    def update_spot(self):
        try:
            # 获取用户选择的显示模式（检查是否存在复选框）
            show_all_fields = self.ui.checkBoxShowAllFields.isChecked() if hasattr(self.ui, 'checkBoxShowAllFields') else False
            selected_field = self.ui.comboBoxSpotField.currentIndex()
            
            # 初始化点列图显示区域
            if show_all_fields:
                self.spot_widget.init_scatter(self.fields_num, True)
            else:
                self.spot_widget.init_scatter(1, False, selected_field)
            
            # 计算光迹追踪
            self.sopt_raytrace(show_all_fields, selected_field)

            # 使用Zemax API进行点列图分析
            self.spot = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.StandardSpot)
            spot_setting = self.spot.GetSettings()
            wave_index = self.ui.comboBoxSpotWavelength.currentIndex()
            spot_setting.Wavelength.SetWavelengthNumber(wave_index)
            spot_setting.RayDensity = self.ui.spinBoxSpotRayDensity.value()
            spot_setting.Pattern = self.ZOSAPI.Analysis.Settings.Spot.Patterns.Hexapolar
            spot_setting.ReferTo = self.ui.comboBoxSpotReferTo.currentIndex()
            
            # 更新表格数据
            self.ui.tableWidgetSpot.setColumnCount(2)
            self.ui.tableWidgetSpot.setHorizontalHeaderLabels(['RMS', 'GEO'])
            
            # 清空表格
            row_count = self.ui.tableWidgetSpot.rowCount()
            for row in range(row_count - 1, -1, -1):
                self.ui.tableWidgetSpot.removeRow(row)
            
            # 计算要显示的视场范围
            fields_to_show = range(self.fields_num) if show_all_fields else [selected_field]
            
            # 针对每个视场计算RMS和GEO值
            for i, field_idx in enumerate(fields_to_show):
                # 设置视场
                spot_setting.Field.SetFieldNumber(field_idx + 1)
                # 计算点列图
                self.spot.ApplyAndWaitForCompletion()
                # 获取结果
                spot_results = self.spot.GetResults()
                
                # 添加到表格
                self.ui.tableWidgetSpot.insertRow(i)
                # 设置行标题
                self.ui.tableWidgetSpot.setVerticalHeaderItem(i, QTableWidgetItem(f'Field {field_idx+1}'))
                
                # 获取并显示RMS和GEO数据
                rms_value = spot_results.SpotData.GetRMSSpotSizeFor(field_idx+1, wave_index)
                geo_value = spot_results.SpotData.GetGeoSpotSizeFor(field_idx+1, wave_index)
                
                rms_item = QTableWidgetItem(f"{rms_value:.3f}")
                geo_item = QTableWidgetItem(f"{geo_value:.3f}")
                
                self.ui.tableWidgetSpot.setItem(i, 0, rms_item)
                self.ui.tableWidgetSpot.setItem(i, 1, geo_item)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算点列图出错：\n{e}")

    def sopt_raytrace(self, show_all_fields=False, selected_field=0):
        try:
            # 打开批量光线追迹工具
            self.raytrace = self.TheSystem.Tools.OpenBatchRayTrace()
            nsur = self.TheSystem.LDE.NumberOfSurfaces
            max_rays = self.ui.spinBoxSpotRayDensity.value()
            
            # 创建非偏振光线数据容器
            normUnPolData = self.raytrace.CreateNormUnpol(
                (max_rays + 1) * (max_rays + 1), 
                self.ZOSAPI.Tools.RayTrace.RaysType.Real,
                nsur
            )
            
            # 基本参数设置
            hx = 0.0  # X方向视场为0
            max_wave = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            num_fields = self.TheSystem.SystemData.Fields.NumberOfFields
            
            # 计算最大Y视场和归一化视场值
            max_field = 0.0
            for i in range(1, num_fields + 1):
                if self.TheSystem.SystemData.Fields.GetField(i).Y > max_field:
                    max_field = self.TheSystem.SystemData.Fields.GetField(i).Y
            
            # 获取主波长
            pwav = 0
            for a in range(1, max_wave + 1):
                if self.TheSystem.SystemData.Wavelengths.GetWavelength(a).IsPrimary == 1:
                    pwav = a
            
            # 计算归一化的Y视场
            hy_ary = []
            for field in range(1, num_fields + 1):
                hy = 1 if max_field == 0 else self.TheSystem.SystemData.Fields.GetField(field).Y / max_field
                hy_ary.append(hy)
                # 计算主光线坐标
                chief_ray = self.TheSystem.MFE.GetOperandValue(
                    self.ZOSAPI.Editors.MFE.MeritOperandType.REAY, 
                    nsur, pwav, 0, hy, 0, 0, 0, 0
                )
            
            # 确定要处理的视场范围
            fields_to_trace = range(num_fields) if show_all_fields else [selected_field]
            
            # 确定要处理的波长
            wave_selection = self.ui.comboBoxSpotWavelength.currentIndex()
            waves_to_trace = range(1, max_wave + 1) if wave_selection == 0 else [wave_selection]
            
            # 针对每个选定的视场和波长追迹光线
            for field_idx in fields_to_trace:
                field = field_idx + 1  # Zemax中视场从1开始
                
                for wave_idx, wave in enumerate(waves_to_trace):
                    # 清除之前的数据
                    normUnPolData.ClearData()
                    
                    # 添加随机分布的光线
                    for i in range(1, (max_rays + 1) * (max_rays + 1) + 1):
                        # 随机生成光线在入瞳面上的位置（确保在单位圆内）
                        while True:
                            px = np.random.random() * 2 - 1
                            py = np.random.random() * 2 - 1
                            if px * px + py * py <= 1:
                                break
                        
                        # 添加光线
                        normUnPolData.AddRay(
                            wave,
                            hx,
                            hy_ary[field - 1],
                            px,
                            py,
                            getattr(self.ZOSAPI.Tools.RayTrace.OPDMode, "None")
                        )
                    
                    # 执行光线追迹
                    self.raytrace.RunAndWaitForCompletion()
                    
                    # 读取结果
                    self._read_trace_results(normUnPolData, field_idx, wave_idx)
            
        except Exception as e:
            self.log_framework.error(f"光迹追踪错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"光迹追踪出错：\n{e}")
        finally:
            # 确保在出错时也关闭资源
            if hasattr(self, 'raytrace') and self.raytrace:
                self.raytrace.Close()

    # 辅助方法：读取光线追迹结果
    def _read_trace_results(self, normUnPolData, field_idx, wave_idx):
        try:
            # 读取光线追迹结果
            normUnPolData.StartReadingResults()
            
            # .NET参数占位符
            sysInt = Int32(1)
            sysDbl = Double(1.0)
            
            # 存储当前批次的结果
            x_points = []
            y_points = []
            
            # 读取所有结果
            output = normUnPolData.ReadNextResult(
                sysInt, sysInt, sysInt,
                sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl,
                sysDbl, sysDbl, sysDbl, sysDbl
            )
            
            while output[0]:  # 成功读取结果
                if output[2] == 0 and output[3] == 0:  # 无错误且未渐晕
                    # 存储光线坐标
                    x_points.append(output[4])  # X坐标
                    y_points.append(output[5])  # Y坐标
                
                # 读取下一个结果
                output = normUnPolData.ReadNextResult(
                    sysInt, sysInt, sysInt,
                    sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl,
                    sysDbl, sysDbl, sysDbl, sysDbl
                )
            
            # 绘制散点图
            if x_points and y_points:
                field_display_idx = field_idx if len(self.spot_widget.axes) > 1 else 0
                self.spot_widget.plot_scatter_data(
                    field_display_idx,
                    np.array(x_points), 
                    np.array(y_points),
                    wave_idx
                )
                
        except Exception as e:
            self.log_framework.error(f"读取光线追迹结果出错: {str(e)}")

    # 计算和绘制波前
    def update_wavefront_map(self):
        try:
            self.wavefront_map = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.WavefrontMap)
            # 获取设置API
            wavefront_map_setting = self.wavefront_map.GetSettings()
            # 从UI获取所有的设置参数，进行设置
            current_sampling = self.ui.comboBoxWavefrontMapSampling.currentText()
            if current_sampling == '32x32':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_32x32
            if current_sampling == '64x64':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '128x128':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '256x256':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '512x512':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_512x512

            wavefront_map_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxWavefrontMapWavelength.currentIndex())
            wavefront_map_setting.Field.SetFieldNumber(self.ui.comboBoxWavefrontMapField.currentIndex())
            # 根据更新后的参数重新计算
            self.wavefront_map.ApplyAndWaitForCompletion()
            # 获取计算结果
            self.wavefront_map_result = self.wavefront_map.GetResults()
            self.wavefront_map_values = self.wavefront_map_result.GetDataGrid(0).Values
            self.wavefront_map_npdata = self.zos.reshape(self.wavefront_map_values, self.wavefront_map_values.GetLength(0),
                                 self.wavefront_map_values.GetLength(1))
            # 绘制波前
            self.wavefront_map_widget.plot_grid_image(self.wavefront_map_npdata)
            #print(self.zos.calPV(self.wavefront_map_values))
            # 计算PV RMS
            self.ui.lineEditPV.setText(str(self.zos.calPV(self.wavefront_map_values)))
            # 计算RMS
            self.ui.lineEditRMS.setText(str(self.zos.calRMS(self.wavefront_map_values)))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算波前出错：\n{e}")

    # 计算和绘制MTF，EXAMPLE4
    def update_MTF(self):
        try:
            self.mtf = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.FftMtf)
            mtf_setting = self.mtf.GetSettings()
            current_sampling = self.ui.comboBoxMTFSampling.currentText()
            if current_sampling == '32x32':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_32x32
            if current_sampling == '64x64':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '128x128':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '256x256':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '512x512':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512

            mtf_setting.MaximumFrequency = self.ui.spinBoxMTFFrequency.value()
            mtf_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxMTFWavelength.currentIndex())
            mtf_setting.Field.SetFieldNumber(self.ui.comboBoxMTFField.currentIndex())
            self.mtf.ApplyAndWaitForCompletion()

            mtf_result = self.mtf.GetResults()
            self.mtf_widget.init_line_data(self.ui.spinBoxMTFFrequency.value())

            colors = ('b', 'g', 'r', 'c', 'm', 'y', 'k')
            for seriesNum in range(0, mtf_result.NumberOfDataSeries, 1):
                data = mtf_result.GetDataSeries(seriesNum)

                # get raw .NET data into numpy array
                xRaw = data.XData.Data
                yRaw = data.YData.Data

                x = list(xRaw)
                y = self.zos.reshape(yRaw, yRaw.GetLength(0), yRaw.GetLength(1), True)

                self.mtf_widget.plot_line_data(x, y[0], c=colors[seriesNum])
                self.mtf_widget.plot_line_data(x, y[1], ls='--', c=colors[seriesNum])

        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算MTF出错：\n{e}")

    # PSF
    def update_PSF(self):
        try:
            self.psf = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.FftPsf)
            psf_setting = self.psf.GetSettings()
            current_sampling = self.ui.comboBoxPSFSampling.currentText()
            if current_sampling == '32x32':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_32x32
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '64x64':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '128x128':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '256x256':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
            if current_sampling == '512x512':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_1024x1024

            psf_setting.Rotation = self.ui.comboBoxPSFRotation.currentIndex()
            psf_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxPSFWavelength.currentIndex())
            psf_setting.Field.SetFieldNumber(self.ui.comboBoxPSFField.currentIndex())
            self.psf.ApplyAndWaitForCompletion()

            psf_result = self.psf.GetResults()
            psf_values = psf_result.GetDataGrid(0).Values
            psf_npdata = self.zos.reshape(psf_values,psf_values.GetLength(0),psf_values.GetLength(1))
            self.psf_widget.plot_grid_image(psf_npdata)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算PSF出错：\n{e}")

    # 导入外部zernike文件进行联合仿真
    def addRealZernike(self):
        # 弹出文件对话框
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "select dat file",
            "",
            "DAT Files (*.dat)",
            options=options
        )
        if not file_path:  # 如果未选择文件，返回
            QMessageBox.warning(self, "提示", "未选择任何文件！")
            return
        # 如果选择了文件，解析它
        try:
            with open(file_path, "r") as file:
                lines = file.readlines()

            # 必须是39行
            if len(lines) != 39:
                QMessageBox.critical(self, "错误", "文件格式错误，要求39行！")
                return

            # 读取第一行和第二行
            zernike_num = float(lines[0].strip())
            norm_radius = float(lines[1].strip())

            # 遍历读取剩余的37行
            zernike_coffs = [float(line.strip()) for line in lines[2:]]

            if zernike_num!=37:
                QMessageBox.critical(self, "错误", "文件格式错误，应该使用37项zernike！")
                return

            # 获取当前行数，在tablewidget后进行追加
            row_count = self.ui.tableWidgetRealData.rowCount()
            # 添加新行
            self.ui.tableWidgetRealData.insertRow(row_count)
            # 设置单元格数据，第一列是下拉控件，用来选择对应的镜面
            combo_box = QComboBox()
            combo_box.setToolTip("选择待替换的镜面ID。")
            # 添加所有可选的镜面
            for i in range(0,self.TheSystem.LDE.NumberOfSurfaces):
                combo_box.addItem(str(i))
            self.ui.tableWidgetRealData.setCellWidget(row_count, 0, combo_box)
            self.ui.tableWidgetRealData.setItem(row_count, 1, QTableWidgetItem(str(norm_radius)))
            # 添加37项系数
            for col, value in enumerate(zernike_coffs):
                item = QTableWidgetItem(str(value))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.ui.tableWidgetRealData.setItem(row_count, col+2, item)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"解析文件时出错：\n{e}")

    # 删除选择的zernike，选择整行才能删除
    def delRealZernike(self):
        try:
            # 获取当前选中的行
            selected_indexes = self.ui.tableWidgetRealData.selectedIndexes()
            if not selected_indexes:
                QMessageBox.warning(self, "提示", "未选择任何行！")
                return
            # 从表格中删除选中的行
            self.ui.tableWidgetRealData.removeRow(selected_indexes[0].row())

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除zernike出错：\n{e}")

    # 更新计算
    def updateRealZernike(self):
        try:
            self.log_framework.info("开始更新光学系统...")
            TheLDE = self.zos.TheSystem.LDE
            
            # 第一步：更新LensData中的数据到光学系统
            self.log_framework.info("正在应用LensData表格中的数据...")
            self.updateLensDataToSystem()
            
            # 第二步：应用RealDataAnalysis中的Zernike数据
            self.log_framework.info("正在应用Zernike实测数据...")
            # 遍历所有要替换的镜子
            for row in range(self.ui.tableWidgetRealData.rowCount()):
                combo_box = self.ui.tableWidgetRealData.cellWidget(row, 0)
                if not combo_box:  # 确保存在组合框
                    continue
                    
                # 获取替换镜子的ID
                surface_id = int(combo_box.currentText())
                self.log_framework.info(f"正在更新镜面 ID:{surface_id} 的Zernike数据")
                
                # 设置镜子类型为Zernike
                SurfaceType = TheLDE.GetSurfaceAt(surface_id).GetSurfaceTypeSettings(self.ZOSAPI.Editors.LDE.SurfaceType.ZernikeFringePhase)
                TheLDE.GetSurfaceAt(surface_id).ChangeType(SurfaceType)
                TheLDE.GetSurfaceAt(surface_id).GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par13).Value = str(37)
                
                # 设置所有Zernike系数
                for i in range(1, 39):
                    if self.ui.tableWidgetRealData.item(row, i) and self.ui.tableWidgetRealData.item(row, i).text():
                        TheLDE.GetSurfaceAt(surface_id).GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par13 + i).Value = self.ui.tableWidgetRealData.item(row, i).text()

            # 更新光学系统的所有分析
            self.log_framework.info("数据更新完成，开始重新计算系统性能...")
            
            # 更新波前图
            self.log_framework.info("更新波前图...")
            self.update_wavefront_map()
            
            # 更新点列图
            self.log_framework.info("更新点列图...")
            self.update_spot()
            
            # 更新MTF
            self.log_framework.info("更新MTF图...")
            self.update_MTF()
            
            # 更新PSF
            self.log_framework.info("更新PSF图...")
            self.update_PSF()
            
            # 刷新镜头数据表格以显示最新状态
            self.log_framework.info("刷新镜头数据表格...")
            self.display_lens_data()
            
            self.log_framework.info("系统更新完成！")
            QMessageBox.information(self, "成功", "光学系统已完全更新！")
            
        except Exception as e:
            self.log_framework.error(f"系统更新失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"更新计算报错：\n{e}")
        
        # 确保UI刷新
        QApplication.processEvents()

    # 新增方法：将LensData表格中的数据更新到光学系统
    def updateLensDataToSystem(self):
        try:
            TheLDE = self.zos.TheSystem.LDE
            
            # 遍历LensData表格的每一行
            for row in range(self.ui.tableWidgetLensData.rowCount()):
                # 获取镜面ID
                id_item = self.ui.tableWidgetLensData.item(row, 0)
                if not id_item or not id_item.text():
                    continue
                    
                surface_id = int(id_item.text())
                surface = TheLDE.GetSurfaceAt(surface_id)
                
                # 获取表面类型
                type_item = self.ui.tableWidgetLensData.item(row, 1)
                if type_item and type_item.text():
                    surface_type = type_item.text()
                    self.log_framework.debug(f"处理镜面 ID: {surface_id}, 类型: {surface_type}")
                
                # 更新常见属性
                # 注意：表头可能会根据实际情况变化，这里只是示例
                # 尝试更新半径
                radius_col = self.fixed_names.index('Radius') if 'Radius' in self.fixed_names else -1
                if radius_col >= 0:
                    radius_item = self.ui.tableWidgetLensData.item(row, radius_col)
                    if radius_item and radius_item.text() and radius_item.text() != 'inf':
                        try:
                            radius_value = float(radius_item.text())
                            if hasattr(surface, 'Radius'):
                                surface.Radius = radius_value
                                self.log_framework.debug(f"已更新镜面 {surface_id} 的半径为 {radius_value}")
                        except (ValueError, Exception) as e:
                            self.log_framework.debug(f"更新半径失败: {str(e)}")
                
                # 尝试更新厚度
                thickness_col = self.fixed_names.index('Thickness') if 'Thickness' in self.fixed_names else -1
                if thickness_col >= 0:
                    thickness_item = self.ui.tableWidgetLensData.item(row, thickness_col)
                    if thickness_item and thickness_item.text() and thickness_item.text() != 'inf':
                        try:
                            thickness_value = float(thickness_item.text())
                            if hasattr(surface, 'Thickness'):
                                surface.Thickness = thickness_value
                                self.log_framework.debug(f"已更新镜面 {surface_id} 的厚度为 {thickness_value}")
                        except (ValueError, Exception) as e:
                            self.log_framework.debug(f"更新厚度失败: {str(e)}")
                
                # 尝试更新材料
                material_col = self.fixed_names.index('Material') if 'Material' in self.fixed_names else -1
                if material_col >= 0:
                    material_item = self.ui.tableWidgetLensData.item(row, material_col)
                    if material_item and material_item.text():
                        try:
                            material_value = material_item.text()
                            if hasattr(surface, 'Material'):
                                surface.Material = material_value
                                self.log_framework.debug(f"已更新镜面 {surface_id} 的材料为 {material_value}")
                        except Exception as e:
                            self.log_framework.debug(f"更新材料失败: {str(e)}")
                
                # 更新Par参数
                for i, col_name in enumerate(self.par_columns):
                    try:
                        col_index = len(self.fixed_names) + i
                        item = self.ui.tableWidgetLensData.item(row, col_index)
                        if item and item.text():
                            par_number = int(col_name[3:])  # 提取数字，如Par1中的1
                            par_column = getattr(self.ZOSAPI.Editors.LDE.SurfaceColumn, col_name)
                            cell = surface.GetSurfaceCell(par_column)
                            if cell and hasattr(cell, 'Value'):
                                cell.Value = item.text()
                                self.log_framework.debug(f"已更新镜面 {surface_id} 的 {col_name} 为 {item.text()}")
                    except Exception as e:
                        self.log_framework.debug(f"更新 {col_name} 失败: {str(e)}")
            
            self.log_framework.info("LensData表格数据已成功应用到光学系统")
        except Exception as e:
            self.log_framework.error(f"应用LensData数据时出错: {str(e)}")
            raise

    # EXAMPLE15
    def systemPrediction(self):
        TheLDE = self.TheSystem.LDE
        tools = self.TheSystem.Tools
        tools.RemoveAllVariables()  # 每次预测之前先清空所有变量

        # test = self.TheSystem.Tools.OpenLocalOptimization()
        # print('open opt2:', test)
        # test.Cancel()

        self.str_txt = self.str_txt + f"<p>-----预测轮次: <b>{self.prediction_round}</b>-----</p>"
        self.str_txt = self.str_txt + f"<p>预测前: </p>"
        # 1, 添加变量. 遍历添加控件中的所有变量
        if len(self.prediction_variables) > 0:
            for variable in self.prediction_variables:
                mirror_id = variable.mirror_id
                optimization_item = variable.optimization_item
                OptimizationItemList = list(OptimizationItem)
                try:
                    opt_index = OptimizationItemList.index(optimization_item) + 1
                    self.log_framework.debug("mirror id: {0}. optimization type: {1}".format(mirror_id, optimization_item.name))
                    Surface = TheLDE.GetSurfaceAt(mirror_id)            # 获取镜子
                    self.log_framework.debug(optimization_item)
                    if optimization_item == OptimizationItem.ZDIS:
                        self.log_framework.debug("make thickness solve variable")
                        Surface.ThicknessCell.MakeSolveVariable()
                        variable.last_value = Surface.Thickness
                        self.str_txt = self.str_txt + f"<p>镜片ID: <b>{mirror_id}</b>; 优化类型: 光轴方向间隔</p>"
                        pass
                    elif optimization_item == OptimizationItem.RADIUS:
                        #Surface.FrontRadiusCell.MakeSolveVariable()
                        variable.last_value = Surface.Radius
                        self.str_txt = self.str_txt + f"<p>镜片ID: <b>{mirror_id}</b>; 优化类型: 镜片半径</p>"
                        pass
                    #elif optimization_item.name == OptimizationItem.RADIUS:
                    #    DecenterCell
                    #    pass

                except ValueError:
                    self.log_framework.error("optimization item out of range")

            self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test.zos');
        else:
            self.log_framework.warning("set at last one variable!")
            return

        #2 定义优化函数
        self.TheMFE = self.TheSystem.MFE
        OptWizard = self.TheMFE.SEQOptimizationWizard

        # [TODO] 与zemax的优化配置不一样
        if self.ui.radioButtonPredictionRMS.isChecked():
            self.log_framework.info("using wavefront RMS as prediction object")
            # Optimize for smallest RMS Spot, which is "Data" = 1

            OptWizard.Data = 0      # wavefront
            OptWizard.Type = 0      # 0:RMS, 1:PTV
            OptWizard.OverallWeight = 1
            # Gaussian Quadrature with 3 rings (refers to index number = 2)
            OptWizard.PupilIntegrationMethod = 0
            OptWizard.Ring = 2 # 3 ring
            OptWizard.Arm = 0 # 6 arm
            # Set air & glass boundaries
            # [TODO]
            # And click OK!
            OptWizard.Apply()

            # mf_filename = 'D:/code/gitproject/OpticalRealSim/RMS_Spot_Radius.mf'
            # TheMFE.SaveMeritFunction(mf_filename)
            # TheMFE.LoadMeritFunction(mf_filename)
            # self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test2.zos')

            self.str_txt = self.str_txt + f"<p>优化目标: 波前RMS; </p>"
            pass

        if self.ui.radioButtonPredictionSpot.isChecked():
            self.log_framework.info("using Spot RMS radius as prediction object")
            # Optimize for smallest RMS Spot, which is "Data" = 1
            OptWizard.Data = 1
            OptWizard.Type = 0      #0:RMS, 1:PTV(Geometric)
            OptWizard.OverallWeight = 1
            # Gaussian Quadrature with 3 rings (refers to index number = 2)
            OptWizard.PupilIntegrationMethod = 0
            OptWizard.Ring = 2  # 3 ring
            OptWizard.Arm = 0  # 6 arm
            # Set air & glass boundaries
            # [TODO]
            # And click OK!
            OptWizard.Apply()

            # 保存优化文件
            # mf_filename = 'D:/code/gitproject/OpticalRealSim/RMS_Spot_Radius.mf'
            # TheMFE.SaveMeritFunction(mf_filename)
            # TheMFE.LoadMeritFunction(mf_filename)
            # self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test2.zos')

            self.str_txt = self.str_txt + f"<p>优化目标: 点列图RMS半径; </p>"

            pass

        # 自定义，此时load启用
        if self.ui.radioButtonPredictionCustom.isChecked():
            self.TheMFE.LoadMeritFunction(self.mf_file)
            pass

        # 3 开始优化
        # Run local optimization and measure time
        t = time.time()
        LocalOpt = tools.OpenLocalOptimization()
        print(LocalOpt)
        if (LocalOpt != None):
            LocalOpt.Algorithm = self.ZOSAPI.Tools.Optimization.OptimizationAlgorithm.DampedLeastSquares        # 阻尼最小二乘法
            LocalOpt.Cycles = self.ZOSAPI.Tools.Optimization.OptimizationCycles.Automatic                       # 迭代: 自动
            LocalOpt.NumberOfCores = os.cpu_count() # 可用的cpu数目
            self.log_framework.info('Local Optimization...')
            self.str_txt = self.str_txt + f"<p>采用局部优化，阻尼最小二乘法，CPU数目<b>{os.cpu_count()}</b> </p>"
            self.log_framework.info('Initial objects value: {0}'.format(LocalOpt.InitialMeritFunction))
            self.str_txt = self.str_txt + f"<p>预测前目标函数: <b>{LocalOpt.InitialMeritFunction}</b> </p>"

            LocalOpt.RunAndWaitForCompletion()
            self.log_framework.info('Final objects value: {0}'.format(LocalOpt.CurrentMeritFunction))
            self.str_txt = self.str_txt + f"<p>预测后目标函数: <b>{LocalOpt.CurrentMeritFunction}</b> </p>"
            LocalOpt.Close()

            pass
        else:
            self.log_framework.debug("open local optimization failed")
        pass

        elapsed = time.time() - t
        self.log_framework.info('Time elapsed: ' + str(round(elapsed, 3)) + 's')
        #self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test3.zos')
        self.str_txt = self.str_txt + f"<p>预测时间: <b>{round(elapsed, 3)}</b> </p>"

        # 获取优化后变量的数值
        for variable in self.prediction_variables:
            mirror_id = variable.mirror_id
            Surface = TheLDE.GetSurfaceAt(mirror_id)
            #print(variable.last_value)
            #print(Surface.Thickness)
            self.str_txt = self.str_txt + f"<p>优化前: <b>{variable.last_value}</b> </p>"
            self.str_txt = self.str_txt + f"<p>优化后: <b>{Surface.Thickness}</b> </p>"
            opt_value = Surface.Thickness - variable.last_value
            opt_value = opt_value * 1000 # 转换为um
            optimization_item = variable.optimization_item
            if optimization_item == OptimizationItem.ZDIS:
                self.str_txt = self.str_txt + f"<p>优化建议: 镜子(ID=<b>{mirror_id}</b>)Z向移动(<b>{round(opt_value,3)}um</b>) </p>"
                pass
            elif optimization_item == OptimizationItem.RADIUS:
                pass

        self.ui.textBrowserPrediction.setHtml(self.str_txt)
        cursor = self.ui.textBrowserPrediction.textCursor()
        cursor.movePosition(cursor.End)
        self.ui.textBrowserPrediction.setTextCursor(cursor)
        self.prediction_round = self.prediction_round + 1

    def addPredictionVariable(self):
        dialog = AddVariableDialog (self.nsur, self)
        if dialog.exec_() == QDialog.Accepted:
            mirror_id, optimization_item = dialog.get_selected_values()
            self.prediction_variables.append(PredictionVariable(mirror_id, optimization_item))
            self.update_prediction_table()
        pass

    def deletePredictionVariable(self):
        selected_rows = set(index.row() for index in self.ui.tableWidgetPedictionVariables.selectedIndexes())
        if selected_rows:
            for row in sorted(selected_rows, reverse=True):
                del self.prediction_variables[row]
                self.ui.tableWidgetPedictionVariables.removeRow(row)
        pass

    def deleteAllPredictionVariable(self):
        self.prediction_variables = []
        self.ui.tableWidgetPedictionVariables.setRowCount(0)
        pass

    def update_prediction_table(self):
        self.ui.tableWidgetPedictionVariables.setRowCount(len(self.prediction_variables))
        self.ui.tableWidgetPedictionVariables.horizontalHeader().setSectionResizeMode(len(self.prediction_variables)-1, QHeaderView.Stretch)
        for row, variable in enumerate(self.prediction_variables):
            mirror_id_item = QTableWidgetItem(str(variable.mirror_id))
            optimization_item_item = QTableWidgetItem(variable.optimization_item.name)
            self.ui.tableWidgetPedictionVariables.setItem(row, 0, mirror_id_item)
            self.ui.tableWidgetPedictionVariables.setItem(row, 1, optimization_item_item)
        pass

    def loadPredictionConfigFile(self):
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_filter = "Optimization file (*.mf);;Merit Function file (*.mf)"  # 根据需求指定文件后缀过滤条件
        file_name, _ = QFileDialog.getOpenFileName(self, "select file", "", file_filter, options=options)
        # 如果读取到了zemax文件，执行操作
        if file_name:
            self.mf_file = file_name
            pass
        else:
            self.log_framework.warning("请选择优化mf文件")
            return
        pass

    def onPredictionRadioChanged(self):
        if self.ui.radioButtonPredictionRMS.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(False)
            pass
        elif self.ui.radioButtonPredictionSpot.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(False)
            pass
        elif self.ui.radioButtonPredictionCustom.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(True)
            pass
        pass

    # 公差敏感性分析配置
    def sensitivityConfig(self):
        try:
            # 先检查并清理已有的self.tol对象
            if hasattr(self, 'tol') and self.tol is not None:
                try:
                    self.tol.Close()
                    self.tol = None
                    self.log_framework.info("已关闭并清理之前的公差分析对象")
                except Exception as e:
                    self.log_framework.warning(f"清理之前的公差分析对象时出错: {str(e)}")
                    self.tol = None
            
            # 继续原有的公差敏感性分析配置
            dialog = SensivitityDialog(self.nsur, self)
            if dialog.exec_() == QDialog.Accepted:
                settings = dialog.get_sensitivity_settings()
                
                # 设置敏感性分析参数
                self.tol = self.TheSystem.Tools.OpenTolerancing()
                self.tol.SetupMode = self.ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity
                
                # 设置评价准则
                if settings['criterion'] == "RMS Spot Radius":
                    self.tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
                else:
                    self.tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSWavefront

                # 设置采样
                self.tol.Sampling = int(settings['sampling'])
                
                # 设置蒙特卡洛次数
                self.tol.NumberOfMonteCarloRuns = settings['monte_carlo_runs']

                self.log_framework.info("公差敏感性分析配置完成")
                return True
                
            self.log_framework.info("用户取消了公差敏感性分析配置")
            return False
        except Exception as e:
            self.log_framework.error(f"公差敏感性分析配置出错: {str(e)}")
            print(str(e))
            return False

    # EXAMPLE14 公差分析：SEQToleranceWizard  OpenTolerancing (ITolerancing)
    def sensitivityAnalysis(self):
        t = time.time()
        # 检查是否已配置
        if not self.tol:
            self.log_framework.error("请先进行公差敏感性分析配置")
            return
            
        # 显示当前公差操作树状态
        tde = self.TheSystem.TDE
        self.log_framework.info("\n当前公差操作树状态:")
        self.log_framework.info("=" * 60)
        if tde.NumberOfOperands > 0:
            for i in range(1, tde.NumberOfOperands + 1):
                operand = tde.GetOperandAt(i)
                self.log_framework.info(f"\n操作数 {i}:")
                self.log_framework.info(f"  类型: {operand.Type}")
                self.log_framework.info(f"  参数1: {operand.Param1}")
                self.log_framework.info(f"  参数2: {operand.Param2}")
                self.log_framework.info(f"  参数3: {operand.Param3}")
                self.log_framework.info(f"  参数4: {operand.Nominal}")
                self.log_framework.info(f"  最小值: {operand.Min}")
                self.log_framework.info(f"  最大值: {operand.Max}")
                if operand.Comment:
                    self.log_framework.info(f"  注释: {operand.Comment}")
                self.log_framework.info("-" * 40)
        else:
            self.log_framework.info("当前没有公差操作数")
        self.log_framework.info("=" * 60)
        # 添加操作数类型代码的调试信息
        self.log_framework.info("公差树原始操作数类型代码信息:")
        self.log_framework.info("=" * 60)
        for i in range(1, tde.NumberOfOperands + 1):
            operand = tde.GetOperandAt(i)
            self.log_framework.info(f"操作数 {i}:")
            self.log_framework.info(f"  原始类型代码: {operand.Type}")
            self.log_framework.info(f"  映射后类型名: {self.getSensitivityOperandName(operand.Type)}")
            self.log_framework.info("-" * 40)
        self.log_framework.info("=" * 60)
            
        # 运行公差分析
        self.tol.RunAndWaitForCompletion()
        self.tol.Close()
        self.tol = None  # 设置为None，确保资源被正确释放
        elapsed = time.time() - t
        self.log_framework.info('敏感性分析耗时: ' + str(round(elapsed, 3)) + '秒')
        
        # 更新UI显示结果
        self.updateSensitivityResultUI()
        pass

    def updateSensitivityResultUI(self):
        self.ui.textBrowserSensitivity.clear()          
        # 打开数据窗口，获取数据
        tolDataView = self.TheSystem.Tools.OpenToleranceDataViewer()
        # 等待数据加载完成
        tolDataView.RunAndWaitForCompletion()              
        # 获取敏感性数据
        SData = tolDataView.SensitivityData
                
        # 显示基本信息
        print('NumberOfCriteria: ', SData.NumberOfCriteria)
        print('NumberOfCompensators: ', SData.NumberOfCompensators)
        print('NumberOfResultOperands: ', SData.NumberOfResultOperands)
        
        # 添加操作数类型代码的调试信息
        self.log_framework.info("\n操作数类型代码信息:")
        self.log_framework.info("=" * 60)
        for i in range(SData.NumberOfResultOperands):
            operand = SData.GetOperand(i)
            self.log_framework.info(f"操作数 {i+1}:")
            self.log_framework.info(f"  原始类型代码: {operand.OperandType}")
            self.log_framework.info(f"  映射后类型名: {self.getSensitivityOperandName(operand.OperandType)}")
            self.log_framework.info("-" * 40)
        self.log_framework.info("=" * 60)
        
        self.ui.textBrowserSensitivity.append("分析元素数目:" + str(SData.NumberOfResultOperands))
        
        # 创建数据容器
        sdc = SensitivityDataContainer()

        # 处理每个操作数#shd，SData.GetOperand(i).OperandType获取到的操作数类型值与tde.GetOperandAt(i).Type获取到的操作数类型值不一样
        for i in range(SData.NumberOfResultOperands):
            self.ui.textBrowserSensitivity.append('type: ' + self.getSensitivityOperandName(SData.GetOperand(i).OperandType) + ' Minimum: ' + str(SData.GetOperand(i).Minimum) +' Maximum: ' + str(SData.GetOperand(i).Maximum) + ' min: ' + str(SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum) +' max: ' + str(SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum))
            #print('Maximum: ', SData.GetOperand(i).Maximum)
            #print('min: ', SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum)
            #print('max: ', SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum)
            sdc.append_data(operand_type = self.getSensitivityOperandName(SData.GetOperand(i).OperandType), operand_value = SData.GetOperand(i).Minimum, operand_change = SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum)
            sdc.append_data(self.getSensitivityOperandName(SData.GetOperand(i).OperandType), SData.GetOperand(i).Maximum, SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum)
            pass
        sdc.sort_data_descending()
        for data in sdc.data_list:
            self.ui.textBrowserSensitivity.append(
                f'Type: {data.operand_type}, Value: {data.operand_value}, Change: {data.operand_change}')

        sdc.clear_data()
        tolDataView.Close()
        pass

    def getSensitivityOperandName(self, n):
        if n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEDX:
            return 'TEDX'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEDY:
            return 'TEDY'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TETX:
            return 'TETX'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TETY:
            return 'TETY'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEXI:
            return 'TEXI'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEZI:
            return 'TEZI'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.COMP:
            return 'COMP'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TWAV:
            return 'TWAV'
        else:
            # 对于未知的操作数类型，返回类型编号
            return f'Unknown({str(n)})'

    def update_optical_system_view(self):
        """
        更新光学系统视图
        """
        # 检查LensData表是否有数据
        if not hasattr(self, 'lens_data') or not self.lens_data:
            self.log_framework.warning("没有镜片数据可供显示")
            return
        
        # 查找主镜（第一个具有非零ID且不是coordinate break的镜片）
        primary_mirror_index = -1
        for i, lens in enumerate(self.lens_data):
            if (lens.get('ID', '0') != '0' and 
                'Coordinate Break' not in str(lens.get('Surface Type', ''))):
                primary_mirror_index = i
                break
        
        # 如果找到了主镜，更新光学系统视图
        if primary_mirror_index >= 0:
            self.log_framework.info(f"更新光学系统视图，主镜索引: {primary_mirror_index}")
            self.optical_system_widget.set_lens_data(self.lens_data, primary_mirror_index)
            self.optical_system_widget.canvas.draw()  # 确保立即重绘
            self.log_framework.info(f"光学系统视图更新完成")
        else:
            self.log_framework.warning("未找到主镜，无法更新光学系统视图")

    # 添加新方法: 处理标签页切换事件
    def on_tab_changed(self, index):
        """当标签页切换时调用"""
        tab_name = self.ui.tabWidget.tabText(index)
        self.log_framework.info(f"切换到标签页: {tab_name}")
        
        # 如果切换到Inspection标签页，重新绘制光学系统视图
        if "Inspection" in tab_name and hasattr(self, 'optical_system_widget'):
            self.log_framework.info("检测到切换到Inspection标签页，刷新光学系统视图")
            if hasattr(self, 'lens_data') and self.lens_data:
                self.update_optical_system_view()
            else:
                self.log_framework.info("没有光学系统数据可供显示")

    def load_tolerance_file(self):
        """
        根据当前加载的zmx文件路径，找到并保存对应的无tolerance后缀的zmx文件路径
        例如：从0815_double_mirror_position_tolerance.zmx 找到 0815_double_mirror_position.zmx
        仅保存文件路径，不加载文件，只在Assembly Tolerance分析时使用
        """
        try:
            # 检查是否已加载Zemax文件
            if not hasattr(self, 'zemax_filename') or not self.zemax_filename:
                self.log_framework.warning("请先加载Zemax文件")
                QMessageBox.warning(self, "提示", "请先加载Zemax文件")
                return
            
            # 获取当前Zemax文件路径
            zemax_path = self.zemax_filename
            self.log_framework.info(f"当前Zemax文件路径: {zemax_path}")
            
            # 提取文件名（不含扩展名）
            file_name = os.path.basename(zemax_path)
            file_name_without_ext = os.path.splitext(file_name)[0]
            self.log_framework.info(f"当前Zemax文件名: {file_name_without_ext}")
            
            # 如果文件名包含"_tolerance"，则移除
            if "_tolerance" in file_name_without_ext:
                base_name = file_name_without_ext.replace("_tolerance", "")
            else:
                base_name = file_name_without_ext
                
            self.log_framework.info(f"基础文件名: {base_name}")
            
            # 构造zmx文件路径
            dir_path = os.path.dirname(zemax_path)
            zmx_file_path = os.path.join(dir_path, base_name + ".zmx")
            self.log_framework.info(f"构造的zmx文件路径: {zmx_file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(zmx_file_path):
                self.log_framework.warning(f"zmx文件不存在: {zmx_file_path}")
                QMessageBox.warning(self, "文件不存在", f"找不到对应的zmx文件:\n{zmx_file_path}")
                return
            
            # 只保存文件路径，不加载文件
            self.assembly_tolerance_filename = zmx_file_path
            self.log_framework.info(f"已保存Assembly Tolerance文件路径: {self.assembly_tolerance_filename}")
            
            # 提示用户
            QMessageBox.information(self, "加载成功", f"已成功找到Assembly Tolerance分析文件:\n{zmx_file_path}")
        
        except Exception as e:
            self.log_framework.error(f"查找Assembly Tolerance文件时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"查找Assembly Tolerance文件时出错:\n{str(e)}")

    def check_assembly_tolerance(self):
        """
        根据Assembly Tolerance中的Evaluation Criterion选项计算相应的值
        将计算结果显示在limit输入框中
        """
        try:
            # 检查是否已设置Assembly Tolerance文件
            if not hasattr(self, 'assembly_tolerance_filename') or not self.assembly_tolerance_filename:
                # 如果未设置，使用当前加载的文件
                self.log_framework.info("未设置Assembly Tolerance文件，使用当前加载的文件")
                assembly_file = self.zemax_filename
            else:
                # 使用专门为Assembly Tolerance设置的文件
                assembly_file = self.assembly_tolerance_filename
                self.log_framework.info(f"使用Assembly Tolerance文件: {assembly_file}")
            
            # 保存当前文件路径，用于后续恢复
            original_file = self.zemax_filename
            
            # 临时加载Assembly Tolerance文件进行分析
            if assembly_file != original_file:
                self.log_framework.info(f"临时加载文件进行分析: {assembly_file}")
                self.TheSystem.LoadFile(assembly_file, False)
            
            # 获取当前选择的评估标准
            criterion_index = self.ui.comboBoxCriterion.currentIndex()
            criterion_text = self.ui.comboBoxCriterion.currentText()
            sampling_index = self.ui.comboBoxSampling.currentIndex()
            sampling_value = sampling_index + 1  # 从1开始计数
            
            self.log_framework.info(f"计算组装公差值: {criterion_text}, 采样值: {sampling_value}")
            
            # 根据选择的标准调用不同的API函数计算值
            result = None
            
            if criterion_index == 0:  # RMS Spot Radius
                result = self.calculate_rms_spot_radius(sampling_value)
            elif criterion_index == 1:  # RMS Wavefront
                result = self.calculate_rms_wavefront(sampling_value)
            elif criterion_index == 2:  # Geom. MTF Avg
                try:
                    mtf_frequency = float(self.ui.lineEditMTFFrequency.text())
                    if mtf_frequency <= 0:
                        raise ValueError("MTF频率必须大于0")
                except ValueError as e:
                    self.log_framework.warning(f"MTF频率无效: {str(e)}")
                    QMessageBox.warning(self, "提示", f"MTF频率无效: {str(e)}")
                    return
                
                result = self.calculate_geom_mtf_avg(sampling_value, mtf_frequency)
            elif criterion_index == 3:  # Diff. MTF Avg
                try:
                    mtf_frequency = float(self.ui.lineEditMTFFrequency.text())
                    if mtf_frequency <= 0:
                        raise ValueError("MTF频率必须大于0")
                except ValueError as e:
                    self.log_framework.warning(f"MTF频率无效: {str(e)}")
                    QMessageBox.warning(self, "提示", f"MTF频率无效: {str(e)}")
                    return
                
                result = self.calculate_diff_mtf_avg(sampling_value, mtf_frequency)
            
            # 恢复原始文件（如果使用了不同的文件）
            if assembly_file != original_file:
                self.log_framework.info(f"恢复原始文件: {original_file}")
                self.TheSystem.LoadFile(original_file, False)
            
            # 显示计算结果
            if result is not None:
                # 格式化输出为指定小数位
                formatted_result = f"{result:.6f}"
                self.ui.lineEditLimit.setText(formatted_result)
                self.log_framework.info(f"计算结果: {formatted_result}")
            else:
                self.log_framework.warning("计算结果为空")
                self.ui.lineEditLimit.setText("")
        
        except Exception as e:
            # 确保在发生错误时也恢复原始文件
            if 'original_file' in locals() and hasattr(self, 'TheSystem'):
                try:
                    self.TheSystem.LoadFile(original_file, False)
                    self.log_framework.info(f"发生错误后恢复原始文件: {original_file}")
                except:
                    pass
                    
            self.log_framework.error(f"计算组装公差值时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"计算出错：\n{str(e)}")

    # 以下是API计算函数，实际实现需要根据ZEMAX API文档进行调整
    def calculate_rms_spot_radius(self, sampling):
        """计算RMS Spot Radius"""
        try:
            # 记录调用情况，但不使用start_surface和stop_surface参数
            self.log_framework.info(f"计算整个光学系统的RMS Spot Radius，采样值={sampling}")
            
            # 创建Spot Diagram分析
            spot_analysis = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.StandardSpot)
            
            # 获取设置
            spot_setting = spot_analysis.GetSettings()
            
            # 使用sampling参数设置光线密度（如果合适的话）
            spot_setting.RayDensity = max(5, sampling * 5)  # 确保光线密度有合理值
            
            # 获取当前选择的视场和波长
            field_idx = self.ui.comboBoxSpotField.currentIndex() + 1  # Zemax中视场从1开始
            wave_idx = self.ui.comboBoxSpotWavelength.currentIndex()
            
            # 设置视场和波长
            if wave_idx > 0:  # 如果不是"All"选项
                spot_setting.Wavelength.SetWavelengthNumber(wave_idx)
            # 设置视场
            spot_setting.Field.SetFieldNumber(field_idx)
            
            # 执行分析
            spot_analysis.ApplyAndWaitForCompletion()
            
            # 获取结果
            spot_results = spot_analysis.GetResults()
            
            # 获取RMS Spot Radius
            rms_spot = spot_results.SpotData.GetRMSSpotSizeFor(field_idx, wave_idx if wave_idx > 0 else 1)
            
            # 关闭分析资源
            spot_analysis.Close()
            
            # 记录原始值
            self.log_framework.info(f"原始RMS Spot Radius: {rms_spot:.6f}")
            
            # 进行单位转换 - 除以632.8
            converted_rms_spot = rms_spot / 632.8
            
            # 记录转换后的值
            self.log_framework.info(f"转换后RMS Spot Radius: {converted_rms_spot:.6f}")
            
            return converted_rms_spot
            
        except Exception as e:
            self.log_framework.error(f"计算RMS Spot Radius出错: {str(e)}")
            # 出错时返回模拟值，也需要进行转换
            return (0.0025 * sampling) / 632.8  # 保留原有的模拟计算作为备选，但进行单位转换

    def calculate_rms_wavefront(self, sampling):
        """计算RMS Wavefront"""
        try:
            self.log_framework.info(f"计算RMS Wavefront，采样值={sampling}")
            
            # 创建Wavefront Map分析
            wavefront_analysis = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.WavefrontMap)
            
            # 获取设置
            settings = wavefront_analysis.GetSettings()
            
            # 1. 视场与波长设置
            field_idx = self.ui.comboBoxWavefrontMapField.currentIndex()  # Zemax API视场从0开始
            wave_idx = self.ui.comboBoxWavefrontMapWavelength.currentIndex()  # Zemax API波长从0开始
            
            settings.Field.SetFieldNumber(field_idx)
            settings.Wavelength.SetWavelengthNumber(wave_idx)
            
            # 2. 采样与控制参数
            # 根据采样值设置采样密度
            if sampling == 1:
                settings.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_32x32 
            elif sampling == 2:
                settings.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            elif sampling == 3:
                settings.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            elif sampling == 4:
                settings.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            else:
                settings.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_512x512
            
            # 其他重要参数设置
            settings.UseExitPupil = True         # 使用出瞳坐标系
            settings.RemoveTilt = True           # 移除波前倾斜项
            settings.ReferenceToPrimary = True   # 相对于主光线参考计算
            
            # 执行分析
            self.log_framework.info("正在执行WavefrontMap分析...")
            wavefront_analysis.ApplyAndWaitForCompletion()
            
            # 获取结果
            self.log_framework.info("正在获取WavefrontMap分析结果...")
            wavefront_results = wavefront_analysis.GetResults()
            
            # 使用get_DataGrids()方法获取数据网格
            self.log_framework.info("尝试使用get_DataGrids()方法获取数据")
            
            # 根据Zemax API的具体规范，方法名可能有所不同
            # 尝试几种可能的方法名
            data_grids = None
            
            # 记录可用方法
            for method_name in dir(wavefront_results):
                if callable(getattr(wavefront_results, method_name)) and not method_name.startswith('_'):
                    self.log_framework.info(f"方法: {method_name}()")
            
            # 尝试获取数据网格
            if hasattr(wavefront_results, 'get_DataGrids'):
                self.log_framework.info("使用get_DataGrids()方法")
                data_grids = wavefront_results.get_DataGrids()
            elif hasattr(wavefront_results, 'GetDataGrids'):
                self.log_framework.info("使用GetDataGrids()方法")
                data_grids = wavefront_results.GetDataGrids()
            elif hasattr(wavefront_results, 'DataGrids'):
                self.log_framework.info("使用DataGrids属性")
                data_grids = wavefront_results.DataGrids
            elif hasattr(wavefront_results, 'GetDataGrid'):
                self.log_framework.info("使用GetDataGrid()方法")
                data_grids = [wavefront_results.GetDataGrid()]
            
            # 处理数据网格并计算RMS
            if data_grids is not None:
                import numpy as np
                self.log_framework.info(f"获取到数据网格: {type(data_grids).__name__}, 数量: {len(data_grids) if hasattr(data_grids, '__len__') else '未知'}")
                
                # 如果是单个数据网格，转换为列表
                if not hasattr(data_grids, '__len__'):
                    data_grids = [data_grids]
                
                # 处理每个数据网格
                all_values = []
                for i, grid in enumerate(data_grids):
                    self.log_framework.info(f"处理数据网格 {i+1}")
                    
                    # 获取网格尺寸
                    rows = grid.NumberOfRows if hasattr(grid, 'NumberOfRows') else grid.Rows
                    cols = grid.NumberOfColumns if hasattr(grid, 'NumberOfColumns') else grid.Columns
                    self.log_framework.info(f"网格尺寸: {rows} x {cols}")
                    
                    # 收集所有有效数据点
                    valid_values = []
                    for row in range(rows):
                        for col in range(cols):
                            try:
                                # 尝试不同的获取数据的方法
                                if hasattr(grid, 'GetValueAt'):
                                    value = grid.GetValueAt(row, col)
                                elif hasattr(grid, 'Value'):
                                    value = grid.Value[row, col]
                                else:
                                    continue
                                
                                # 排除无效值
                                if not np.isnan(value) and value != 0 and abs(value) < 1e10:
                                    valid_values.append(value)
                            except Exception as e:
                                self.log_framework.debug(f"获取数据点 [{row}, {col}] 时出错: {str(e)}")
                    
                    self.log_framework.info(f"有效数据点数量: {len(valid_values)}")
                    all_values.extend(valid_values)
                
                # 如果有有效数据点，计算RMS
                if all_values:
                    # 计算RMS = sqrt(平均平方)
                    values_array = np.array(all_values)
                    rms_wavefront = np.sqrt(np.mean(np.square(values_array)))
                    self.log_framework.info(f"计算得到RMS波前误差: {rms_wavefront:.6f}")
                else:
                    self.log_framework.warning("未找到有效数据点，使用默认值")
                    rms_wavefront = 0.05 * sampling
            else:
                self.log_framework.warning("未能获取数据网格，使用默认值")
                rms_wavefront = 0.05 * sampling
            
            # 关闭分析资源
            wavefront_analysis.Close()
            
            # 获取当前波长值（单位：微米）
            # 为了精确计算，我们获取主波长的物理长度
            current_wavelength = self.TheSystem.SystemData.Wavelengths.GetWavelength(wave_idx + 1).Wavelength
            self.log_framework.info(f"当前波长: {current_wavelength:.6f} μm")
            
            # 记录原始值（波长单位）
            self.log_framework.info(f"最终使用的RMS Wavefront: {rms_wavefront:.6f} waves")
            
            # 转换为物理长度（微米）
            physical_rms = rms_wavefront * current_wavelength
            self.log_framework.info(f"物理RMS Wavefront: {physical_rms:.6f} μm")
            
            # 进行单位转换 - 除以632.8纳米（0.6328微米）
            converted_value = physical_rms / 0.6328
            self.log_framework.info(f"转换后RMS Wavefront: {converted_value:.6f}")
            
            return converted_value
            
        except Exception as e:
            self.log_framework.error(f"计算RMS Wavefront出错: {str(e)}")
            # 出错时返回模拟值，也需要进行转换
            original_value = 0.05 * sampling
            converted_value = original_value / 632.8
            self.log_framework.info(f"使用模拟值 - 原始RMS Wavefront: {original_value:.6f}，转换后: {converted_value:.6f}")
            return converted_value

    def calculate_geom_mtf_avg(self, sampling, frequency):
        """计算几何MTF平均值"""
        self.log_framework.info(f"计算几何MTF平均值，采样值={sampling}，频率={frequency}")
        # TODO: 使用实际的ZEMAX API调用
        # 模拟返回一个合理的测试值 (0到1之间的值)
        # MTF是比值，不需要进行单位转换
        mtf_value = max(0, min(1, 0.8 - 0.05 * sampling - 0.001 * frequency))
        self.log_framework.info(f"MTF几何平均值: {mtf_value:.6f}")
        return mtf_value

    def calculate_diff_mtf_avg(self, sampling, frequency):
        """计算衍射MTF平均值"""
        self.log_framework.info(f"计算衍射MTF平均值，采样值={sampling}，频率={frequency}")
        # TODO: 使用实际的ZEMAX API调用
        # 模拟返回一个合理的测试值 (0到1之间的值)
        # MTF是比值，不需要进行单位转换
        mtf_value = max(0, min(1, 0.9 - 0.04 * sampling - 0.002 * frequency))
        self.log_framework.info(f"MTF衍射平均值: {mtf_value:.6f}")
        return mtf_value

    def analysis_assembly_tolerance(self):
        """
        执行Assembly Tolerance公差分析
        读取界面上的公差参数值，设置公差分析工具，运行分析并显示结果
        """
        try:
            self.log_framework.info("开始执行Assembly Tolerance公差分析...")
            
            # 检查是否已设置Assembly Tolerance文件
            if not hasattr(self, 'assembly_tolerance_filename') or not self.assembly_tolerance_filename:
                # 如果未设置，使用当前加载的文件
                self.log_framework.info("未设置Assembly Tolerance文件，使用当前加载的文件")
                assembly_file = self.zemax_filename
            else:
                # 使用专门为Assembly Tolerance设置的文件
                assembly_file = self.assembly_tolerance_filename
                self.log_framework.info(f"使用Assembly Tolerance文件: {assembly_file}")
            
            # 保存当前文件路径，用于后续恢复
            original_file = self.zemax_filename
            
            # 临时加载Assembly Tolerance文件进行分析
            if assembly_file != original_file:
                self.log_framework.info(f"临时加载文件进行分析: {assembly_file}")
                self.TheSystem.LoadFile(assembly_file, False)
            
            # 1. 读取界面上的公差参数值
            start_surface = self.ui.spinBoxStartSurface.value()
            stop_surface = self.ui.spinBoxStopSurface.value()
            thickness = self.ui.doubleSpinBoxThickness.value()
            decenter_x = self.ui.doubleSpinBoxDecenterX.value()
            decenter_y = self.ui.doubleSpinBoxDecenterY.value()
            tilt_x = self.ui.doubleSpinBoxTitleX.value()
            tilt_y = self.ui.doubleSpinBoxTitleY.value()
            
            self.log_framework.info(f"公差参数: 起始面={start_surface}, 结束面={stop_surface}, 厚度={thickness}, "
                                   f"偏心X={decenter_x}, 偏心Y={decenter_y}, 倾斜X={tilt_x}, 倾斜Y={tilt_y}")
            
            # 检查表面范围是否有效
            if start_surface >= stop_surface:
                self.log_framework.warning("无效的表面范围：起始面必须小于结束面")
                QMessageBox.warning(self, "参数错误", "起始面必须小于结束面")
                return
            
            # 清空结果显示区域
            self.ui.textBrowserAssembly.clear()
            self.ui.textBrowserAssembly.append(f"执行公差分析...\n")
            self.ui.textBrowserAssembly.append(f"表面范围: {start_surface} - {stop_surface}")
            self.ui.textBrowserAssembly.append(f"厚度公差: {thickness}")
            self.ui.textBrowserAssembly.append(f"偏心公差: X={decenter_x}, Y={decenter_y}")
            self.ui.textBrowserAssembly.append(f"倾斜公差: X={tilt_x}, Y={tilt_y}\n")
            
            # 获取评估标准设置
            criterion_index = self.ui.comboBoxCriterion.currentIndex()
            criterion_text = self.ui.comboBoxCriterion.currentText()
            sampling_index = self.ui.comboBoxSampling.currentIndex()
            sampling_value = sampling_index + 1  # 从1开始计数
            
            # 如果是MTF相关标准，获取MTF频率
            mtf_frequency = 0
            if criterion_index >= 2:  # MTF相关标准
                try:
                    mtf_frequency = float(self.ui.lineEditMTFFrequency.text())
                    if mtf_frequency <= 0:
                        raise ValueError("MTF频率必须大于0")
                except ValueError as e:
                    self.log_framework.warning(f"MTF频率无效: {str(e)}")
                    QMessageBox.warning(self, "参数错误", f"MTF频率无效: {str(e)}")
                    return
            
            # 2. 设置公差向导并应用设置
            self.log_framework.info("配置公差向导...")
            tWiz = self.TheSystem.TDE.SEQToleranceWizard
            
            # 设置表面公差
            # 只设置我们界面上有的参数，其他使用默认值
            tWiz.SurfaceThickness = thickness
            # 禁用偏心和倾斜参数，只使用厚度参数
            tWiz.IsSurfaceDecenterXUsed = False
            tWiz.IsSurfaceDecenterYUsed = False
            tWiz.IsSurfaceTiltXUsed = False
            tWiz.IsSurfaceTiltYUsed = False
            tWiz.IsSurfaceRadiusUsed = False
            
            # 设置元件公差
            # 这里也可以使用与表面相同的公差值
            tWiz.ElementDecenterX = decenter_x
            tWiz.ElementDecenterY = decenter_y
            tWiz.ElementTiltXDegrees = tilt_x
            tWiz.ElementTiltYDegrees = tilt_y
            
            # 指定不使用的公差
            tWiz.IsSurfaceSandAIrregularityUsed = False
            tWiz.IsIndexUsed = False
            tWiz.IsIndexAbbePercentageUsed = False

            # 指定分析起始面和结束面
            tWiz.StartAtSurface = start_surface
            tWiz.StopAtSurface = stop_surface
            
            # 应用公差向导设置
            tWiz.OK()
            self.log_framework.info("已应用公差向导设置")
            
            # 3. 设置公差分析并运行
            self.log_framework.info("启动公差分析...")
            self.ui.textBrowserAssembly.append("正在进行公差分析，请稍候...\n")
            QApplication.processEvents()  # 更新UI
            
            # 打开公差分析工具
            tol = self.TheSystem.Tools.OpenTolerancing()
            
            # 选择逆限制模式而非灵敏度分析模式
            tol.SetupMode = self.ZOSAPI.Tools.Tolerancing.SetupModes.InverseLimit
            
            # 设置评估标准和相关参数
            if criterion_index == 0:  # RMS Spot Radius
                tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
            elif criterion_index == 1:  # RMS Wavefront
                tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSWavefront
            elif criterion_index == 2:  # Geom. MTF Avg
                tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.GeomMTFAverage
                tol.MTFFrequency = mtf_frequency
            elif criterion_index == 3:  # Diff. MTF Avg
                tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.DiffMTFAverage
                tol.MTFFrequency = mtf_frequency
            
            # 设置采样
            tol.CriterionSampling = sampling_value
            
            # 从UI中获取限制值
            
            try:
                limit_value = float(self.ui.lineEditLimit.text())
                tol.MaximumCriteria = limit_value
                self.log_framework.info(f"设置限制值: {limit_value}")
            except ValueError:
                self.log_framework.warning("无法解析限制值，使用默认值")
                tol.MaximumCriteria = 0.008  # 默认值
            
            
            
            # 其他设置
            tol.CriterionComp = self.ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
            tol.CriterionCycle = 2
            tol.CriterionField = self.ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined
            
            # 蒙特卡洛模拟设置，增加运行次数
            tol.NumberOfRuns = 200  # 从20增加到200
            # 不设置NumberToSave，使用默认值
            
            # 运行公差分析
            self.log_framework.info("正在运行公差分析...")
            tol.RunAndWaitForCompletion()
            self.log_framework.info("公差分析完成")
            
            # 4. 获取分析结果
            self.log_framework.info("获取分析结果...")
            
            # 先关闭公差分析工具，释放资源
            tol.Close()
            self.log_framework.info("已关闭公差分析工具")
            
            # 确保系统空闲
            if hasattr(self.TheSystem, 'WaitForIdle'):
                self.TheSystem.WaitForIdle()
                self.log_framework.info("等待系统空闲完成")
            
            # 显示基本信息
            self.ui.textBrowserAssembly.append(f"分析完成")
            self.ui.textBrowserAssembly.append(f"评估标准: {criterion_text}")
            self.ui.textBrowserAssembly.append(f"采样值: {sampling_value}")
            self.ui.textBrowserAssembly.append(f"限制值: {limit_value}")
            
            # 显示当前公差操作树状态
            self.ui.textBrowserAssembly.append("\n当前公差操作树状态:")
            self.ui.textBrowserAssembly.append("=" * 60)
            tde = self.TheSystem.TDE
            
            # 添加操作数类型映射字典，参照Sensitivity.py
            operand_type_map = {
                4: 'TTHI', #镜片间隔
                17: 'TEDX',  # X偏心
                18: 'TEDY',  # Y偏心
                20: 'TETX',  # X倾斜
                21: 'TETY',  # Y倾斜
            }
            
            has_mapped_operands = False
            if tde.NumberOfOperands > 0:
                for i in range(1, tde.NumberOfOperands + 1):
                    operand = tde.GetOperandAt(i)
                    # 获取操作数类型的可读名称
                    operand_type = int(operand.Type)
                    type_name = operand_type_map.get(operand_type)
                    
                    # 仅处理有映射的操作数类型
                    if type_name:
                        has_mapped_operands = True
                        self.ui.textBrowserAssembly.append(f"\n操作数 {i}:")
                        
                        # 根据不同的操作数类型显示不同格式的信息
                        if type_name == 'TTHI':
                            self.ui.textBrowserAssembly.append(f"  镜面之间的间隔为{operand.Param3}的装配公差推荐为: {operand.Min} - {operand.Max}")
                        elif type_name == 'TEDX':
                            self.ui.textBrowserAssembly.append(f"  镜片{operand.Param1}的X方向的偏心的装配公差推荐为: {operand.Min} - {operand.Max}")
                        elif type_name == 'TEDY':
                            self.ui.textBrowserAssembly.append(f"  镜片{operand.Param1}的Y方向的偏心的装配公差推荐为: {operand.Min} - {operand.Max}")
                        elif type_name == 'TETX':
                            self.ui.textBrowserAssembly.append(f"  镜片{operand.Param1}的X方向的倾斜的装配公差推荐为: {operand.Min} - {operand.Max}")
                        elif type_name == 'TETY':
                            self.ui.textBrowserAssembly.append(f"  镜片{operand.Param1}的Y方向的倾斜的装配公差推荐为: {operand.Min} - {operand.Max}")
                            
                        self.ui.textBrowserAssembly.append("-" * 40)
                
                if not has_mapped_operands:
                    self.ui.textBrowserAssembly.append("当前没有可映射的公差操作数")
            else:
                self.ui.textBrowserAssembly.append("当前没有公差操作数")
            self.ui.textBrowserAssembly.append("=" * 60)
                                    
            # 恢复原始文件（如果使用了不同的文件）
            if assembly_file != original_file:
                self.log_framework.info(f"恢复原始文件: {original_file}")
                self.TheSystem.LoadFile(original_file, False)
            
            self.log_framework.info("Assembly Tolerance公差分析完成")
        
        except Exception as e:
            # 确保在发生错误时也恢复原始文件
            if 'original_file' in locals() and hasattr(self, 'TheSystem'):
                try:
                    self.TheSystem.LoadFile(original_file, False)
                    self.log_framework.info(f"发生错误后恢复原始文件: {original_file}")
                except:
                    pass
                    
            self.log_framework.error(f"执行Assembly Tolerance公差分析时出错: {str(e)}")
            self.ui.textBrowserAssembly.append(f"错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行公差分析时出错：\n{str(e)}")