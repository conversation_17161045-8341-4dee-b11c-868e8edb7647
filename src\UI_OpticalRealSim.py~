import os

from PyQt5 import QtCore, QtGui
from PyQt5.QtWidgets import  QFileDialog, QApplication, QPushButton, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtGui import *
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *

import sys
import time
import matplotlib
import numpy as np
import clr
from System import Enum, Int32, Double
from win32comext.shell.demos.explorer_browser import MainWindow

from src.Prediction import AddVariableDialog
from src.Prediction import OptimizationItem
from src.Prediction import PredictionVariable
from src.Sensitivity import SensitivityDataContainer

from src.qLog import LogDisplay
from src.qLog import LogFramework
from src.Sensitivity import SensivitityDialog
from src.Sensitivity import SensitivityDataContainer
from src.Sensitivity import *

matplotlib.use("Qt5Agg")
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt

# 自定义绘图插件
from src.PlotDataWidget import PlotDataWidget

# UI
import ui.OpticalRealSim
# [BUG] 使用分裂期splitter在操作plt控件时，当控件大小为0时，程序闪退
# [TODO] 将控件改为stacked widget
#import OpticalRealSim

# zemax
from src.PythonZemaxApp import PythonStandaloneApplication

class UI_OpticalRealSim(QMainWindow):
    def __init__(self):
        super(UI_OpticalRealSim, self).__init__()
        self.showMaximized()
        self.zemax_filename=''
        #
        self.ui = ui.OpticalRealSim.Ui_MainWindow()
        self.ui.setupUi(self)
        self.ui.pushButtonOpenZemax.clicked.connect(self.open_zemax)
        self.init_parameters()
        self.init_zemax()
        # 设置表头
        self.ui.tableWidgetRealData.setColumnCount(39)
        self.ui.tableWidgetRealData.setHorizontalHeaderLabels(["Name"] + ["Radius"] + [str(i) for i in range(1, 38)])
        # 设置日志
        self.log_display = LogDisplay(self.ui.textBrowserLog)
        self.log_framework = LogFramework(self.log_display)
        self.log_framework.info("Optical Analysis Software based on Measured data.")
        # 预测优化
        self.prediction_variables = []      # 存储所有变量结构体，包括: 镜面ID、变量类型
        self.prediction_last_values = []    #
        self.prediction_round = 0           # 预测次数，用于记录
        self.str_txt = ''
        self.ui.tableWidgetPedictionVariables.setColumnCount(2)
        self.ui.tableWidgetPedictionVariables.setHorizontalHeaderLabels(["Mirror ID", "Optimization Item"])
        self.ui.radioButtonPredictionRMS.clicked.connect(self.onPredictionRadioChanged)
        self.ui.radioButtonPredictionSpot.clicked.connect(self.onPredictionRadioChanged)
        self.ui.radioButtonPredictionCustom.clicked.connect(self.onPredictionRadioChanged)

        self.updateUIStatus(False)

    # 初始化绘制参数
    def init_parameters(self):
        self.sampling_items = ['32x32', '64x64', '128x128', '256x256', '512x512']
        self.rotation_items = ['0', '90', '180', '270']
        # 初始化点列图控件
        self.spot_widget = PlotDataWidget()
        w = self.ui.widget
        if w:
            w_layout = w.layout()
            if w_layout is None:
                w_layout = QVBoxLayout(w)
                w.setLayout(w_layout)
            w_layout.addWidget(self.spot_widget)

        # 初始化波前绘制控件
        self.wavefront_map_widget = PlotDataWidget()
        frame = self.ui.frame
        if frame:
            frame_layout = frame.layout()
            if frame_layout is None:
                frame_layout = QVBoxLayout(frame)
                frame.setLayout(frame_layout)
            frame_layout.addWidget(self.wavefront_map_widget)

        # 初始化MTF
        self.mtf_widget = PlotDataWidget()
        mtf_frame = self.ui.widgetMTF
        if mtf_frame:
            mtf_layout = mtf_frame.layout()
            if mtf_layout is None:
                mtf_layout = QVBoxLayout(mtf_frame)
                mtf_frame.setLayout(mtf_layout)
            mtf_layout.addWidget(self.mtf_widget)

        # 初始化PSF
        self.psf_widget = PlotDataWidget()
        psf_frame = self.ui.widgetPSF
        if psf_frame:
            psf_layout = psf_frame.layout()
            if psf_layout is None:
                psf_layout = QVBoxLayout(psf_frame)
                psf_frame.setLayout(psf_layout)
            psf_layout.addWidget(self.psf_widget)

    # 初始化zemax API
    def init_zemax(self):
        # init ZOS python 初始化ZOS
        self.zos = PythonStandaloneApplication()
        self.ZOSAPI = self.zos.ZOSAPI
        self.TheApplication = self.zos.TheApplication
        self.TheSystem = self.zos.TheSystem

        # test = self.TheSystem.Tools.OpenLocalOptimization()
        # print('open opt:', test)
        # test.Close()

        # Set up Batch Ray Trace
        #self.raytrace = self.TheSystem.Tools.OpenBatchRayTrace()
        #print('raytrace', self.raytrace)
        # self.LocalOpt = self.zos.TheSystem.Tools.OpenGlobalOptimization()
        # print('opt', self.LocalOpt)

    # 打开zemax文件
    def open_zemax(self):
        """
        打开文件选择对话框，过滤指定后缀的文件，并返回选择的文件名（如果有选择）
        """
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_filter = "zemax file (*.zmx);;zemax file (*.zmx)"  # 根据需求指定文件后缀过滤条件
        file_name, _ = QFileDialog.getOpenFileName(self, "select file", "", file_filter, options=options)
        # 如果读取到了zemax文件，执行操作
        if file_name:
            self.updateUIStatus(True)
            self.log_framework.debug("file name {0}".format(file_name))
            self.zemax_filename = file_name
            self.ui.lineEditZemaxFilename.setText(self.zemax_filename)
            self.TheSystem.LoadFile(self.zemax_filename, False)

            self.display_system_data()
            self.display_lens_data()
            # 图表初次显示，参数配置好(指定默认参数)，显示控件按照默认参数进行绘制

            ## spot
            self.ui.spinBoxSpotRayDensity.setValue(30)
            self.ui.spinBoxSpotRayDensity.setMinimum(10)
            self.ui.spinBoxSpotRayDensity.setMaximum(120)
            self.ui.comboBoxSpotReferTo.addItems(['Chief Ray', 'Centroid', 'Middle', 'Vertex'])
            self.ui.comboBoxSpotReferTo.setCurrentIndex(1)  # 默认使用Centroid方法
            self.ui.comboBoxSpotWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxSpotWavelength.addItem(str(n+1))
                pass
            for n in range(self.fields_num):
                self.ui.comboBoxSpotField.addItem(str(n + 1))
                pass

            ## wavefronmap，打开zemax文件后，进行参数设置
            for item in self.sampling_items:
                self.ui.comboBoxWavefrontMapSampling.addItem(item)
            for item in self.rotation_items:
                self.ui.comboBoxWavefrontMapRotation.addItem(item)
            for n in range(self.wavelengths_num):
                self.ui.comboBoxWavefrontMapWavelength.addItem(str(n+1))
                pass
            for n in range(self.fields_num):
                self.ui.comboBoxWavefrontMapField.addItem(str(n + 1))
                pass

            ### MTF参数设置
            self.ui.spinBoxMTFFrequency.setValue(50)
            self.ui.spinBoxMTFFrequency.setMinimum(10)
            self.ui.spinBoxMTFFrequency.setMaximum(150)
            for item in self.sampling_items:
                self.ui.comboBoxMTFSampling.addItem(item)
            self.ui.comboBoxMTFWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxMTFWavelength.addItem(str(n + 1))
            self.ui.comboBoxMTFField.addItem('All')
            for n in range(self.fields_num):
                self.ui.comboBoxMTFField.addItem(str(n + 1))

            ### PSF参数设置
            for item in self.sampling_items:
                self.ui.comboBoxPSFSampling.addItem(item)
            self.ui.comboBoxPSFRotation.addItems(['CW0', 'CW90', 'CW180', 'CW270'])
            self.ui.comboBoxPSFWavelength.addItem('All')
            for n in range(self.wavelengths_num):
                self.ui.comboBoxPSFWavelength.addItem(str(n + 1))
            for n in range(self.fields_num):
                self.ui.comboBoxPSFField.addItem(str(n + 1))

            # 计算和绘制点列图
            self.update_spot()
            self.ui.spinBoxSpotRayDensity.editingFinished.connect(self.update_spot)
            self.ui.comboBoxSpotReferTo.currentIndexChanged.connect(self.update_spot)
            self.ui.comboBoxSpotWavelength.currentIndexChanged.connect(self.update_spot)
            self.ui.comboBoxSpotField.currentIndexChanged.connect(self.update_spot)

            # 计算和绘制波前
            self.update_wavefront_map()
            self.ui.comboBoxWavefrontMapSampling.currentIndexChanged.connect(self.update_wavefront_map)
            self.ui.comboBoxWavefrontMapWavelength.currentIndexChanged.connect(self.update_wavefront_map)
            self.ui.comboBoxWavefrontMapField.currentIndexChanged.connect(self.update_wavefront_map)

            # 计算MTF
            self.update_MTF()
            self.ui.spinBoxMTFFrequency.editingFinished.connect(self.update_MTF)
            self.ui.comboBoxMTFSampling.currentIndexChanged.connect(self.update_MTF)
            self.ui.comboBoxMTFWavelength.currentIndexChanged.connect(self.update_MTF)
            self.ui.comboBoxMTFField.currentIndexChanged.connect(self.update_MTF)

            # 计算PSF
            self.update_PSF()
            self.ui.comboBoxPSFSampling.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFRotation.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFWavelength.currentIndexChanged.connect(self.update_PSF)
            self.ui.comboBoxPSFField.currentIndexChanged.connect(self.update_PSF)


            # 设置信号槽函数，打开zemax后才能允许读取实测数据
            self.ui.pushButtonAdd.clicked.connect(self.addRealZernike)
            self.ui.pushButtonDelete.clicked.connect(self.delRealZernike)
            self.ui.pushButtonUpdate.clicked.connect(self.updateRealZernike)

            # 预测优化
            self.ui.pushButtonPrediction.clicked.connect(self.systemPrediction)
            self.ui.pushButtonAddPredictionVariable.clicked.connect(self.addPredictionVariable)
            self.ui.pushButtonDeletePredictionVariable.clicked.connect(self.deletePredictionVariable)
            self.ui.pushButtonDeleteAllPredictionVariable.clicked.connect(self.deleteAllPredictionVariable)
            self.ui.pushButtonLoadPredictionConfig.clicked.connect(self.loadPredictionConfigFile)

            # 公差分析
            self.ui.pushButtonSensitivityAnalysis.clicked.connect(self.sensitivityAnalysis)
            self.ui.pushButtonSensitivityConfig.clicked.connect(self.sensitivityConfig)
        else:
            print("未选择任何文件")
            return None

    # 更新UI启用和禁止
    def updateUIStatus(self, b):

      self.ui.tabWidget.setEnabled(b)
      self.ui.tabWidget_2.setEnabled(b)
      self.ui.groupBox.setEnabled(b)
      pass

    # 显示系统数据
    def display_system_data(self):
        """
        获取self.TheSystem.SystemData中的系统数据
        self.system_data, key-value, n行2列，第一列是名称，第二列对应值
        """
        self.system_data=[]
        self.system_data.append({"ApertureType": self.TheSystem.SystemData.Aperture.ApertureType})
        self.system_data.append({"ApertureValue": self.TheSystem.SystemData.Aperture.ApertureValue})
        self.fields_num = self.TheSystem.SystemData.Fields.NumberOfFields
        self.wavelengths_num = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
        self.system_data.append({"FieldsNum": self.fields_num})
        self.system_data.append({"WavelengthsNum": self.wavelengths_num})

        self.log_framework.info("fields number: {0}".format(self.fields_num))
        # 遍历视场
        for n in range(self.fields_num):
            field_str = "[X:{0}, Y:{1}, W:{2}, active:{3}]".format(self.TheSystem.SystemData.Fields.GetField(n + 1).X, self.TheSystem.SystemData.Fields.GetField(n + 1).Y, self.TheSystem.SystemData.Fields.GetField(n + 1).Weight, self.TheSystem.SystemData.Fields.GetField(n + 1).IsActive)
            self.system_data.append({"Field" + str(n + 1): field_str})
            pass

        self.log_framework.info("wavelengths number: {0}".format(self.wavelengths_num))
        # 遍历波长
        for n in range(self.wavelengths_num):
            wave_str = "[wavelength:{0}, W:{1}, active:{2}, primary:{3}]".format(self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).Wavelength, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).Weight, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).IsActive, self.TheSystem.SystemData.Wavelengths.GetWavelength(n + 1).IsPrimary)
            self.system_data.append({"Wavelength" + str(n + 1): wave_str})
            pass

        self.system_data.append({"AdjustIndexDataToEnvironment": self.TheSystem.SystemData.Environment.AdjustIndexToEnvironment})
        self.system_data.append({"Temperature": self.TheSystem.SystemData.Environment.Temperature})
        self.system_data.append({"Pressure": self.TheSystem.SystemData.Environment.Pressure})

        # [TODO] 添加其他信息
        self.system_data.append({"LensUnits(0:mm,1:cm,2:inc,3:m)": self.TheSystem.SystemData.Units.LensUnits})
        self.system_data.append({"SourceUnits(0:watts,1:lumens,2:joules)": self.TheSystem.SystemData.Units.SourceUnits})

        self.ui.tableWidgetSystemData.setRowCount(len(self.system_data))
        # 总共两列
        self.ui.tableWidgetSystemData.setColumnCount(2)
        # 第二列设置自动宽度
        self.ui.tableWidgetSystemData.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        # 将所有self.system_data数据进行显示
        for row in range(len(self.system_data)):
            key = list(self.system_data[row].keys())[0]
            value = self.system_data[row][key]
            item_key = QTableWidgetItem(str(key))
            item_value = QTableWidgetItem(str(value))
            self.ui.tableWidgetSystemData.setItem(row, 0, item_key)
            self.ui.tableWidgetSystemData.setItem(row, 1, item_value)

    # 显示镜头数据
    def display_lens_data(self):
        # list容器，每一个镜头数据组成一个list
        self.lens_data = []
        self.nsur = self.TheSystem.LDE.NumberOfSurfaces
        for n in range(self.nsur):
            lens = {}
            lens['ID'] = str(n)
            lens['Surface Type'] = self.TheSystem.LDE.GetSurfaceAt(n).TypeName
            lens['Radius'] = self.TheSystem.LDE.GetSurfaceAt(n).Radius
            lens['Thickness'] = self.TheSystem.LDE.GetSurfaceAt(n).Thickness
            lens['Material'] = self.TheSystem.LDE.GetSurfaceAt(n).Material
            lens['Clear Semi-Diameter'] = self.TheSystem.LDE.GetSurfaceAt(n).SemiDiameter
            self.lens_data.append(list(lens.items()))
            pass
        self.ui.tableWidgetLensData.setRowCount(len(self.lens_data))
        self.ui.tableWidgetLensData.setColumnCount(len(self.lens_data[0]))
        self.ui.tableWidgetLensData.horizontalHeader().setSectionResizeMode(len(self.lens_data[0])-1, QHeaderView.Stretch)

        # 设置表头标签，获取第一个镜头数据列表中的key作为表头
        headers = [key for key, _ in self.lens_data[0]]
        self.ui.tableWidgetLensData.setHorizontalHeaderLabels(headers)

        for row in range(len(self.lens_data)):
            for col in range(len(self.lens_data[row])):
                key, value = self.lens_data[row][col]
                item = QTableWidgetItem(str(value))
                self.ui.tableWidgetLensData.setItem(row, col, item)

    # 计算和绘制点列图 EXAMPLE22
    def update_spot(self):
        try:
            self.sopt_raytrace()

            self.spot = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.StandardSpot)
            spot_setting = self.spot.GetSettings()
            spot_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxSpotWavelength.currentIndex())
            # 计算全部视场
            spot_setting.Field.SetFieldNumber(0)
            spot_setting.RayDensity = self.ui.spinBoxSpotRayDensity.value()
            spot_setting.Pattern = self.ZOSAPI.Analysis.Settings.Spot.Patterns.Hexapolar
            spot_setting.ReferTo = self.ui.comboBoxSpotReferTo.currentIndex()
            #spot_setting.ReferTo = self.ui.comboBoxSpotReferTo.currentIndex()
            self.spot.ApplyAndWaitForCompletion()

            self.spot_results = self.spot.GetResults()

            row_count = self.ui.tableWidgetSpot.rowCount()
            for row in range(row_count - 1, -1, -1):  # 从最后一行开始往前删除
                self.ui.tableWidgetSpot.removeRow(row)

            self.ui.tableWidgetSpot.setColumnCount(2)
            self.ui.tableWidgetSpot.setHorizontalHeaderLabels(['RMS', 'GEO'])
            for n in range(self.fields_num):
                # 添加一行
                row_items = []
                # 设置行表头
                row_header_item = QTableWidgetItem('Field '+ str(n))
                self.ui.tableWidgetSpot.setVerticalHeaderItem(n, row_header_item)
                # RMS
                item1 = QTableWidgetItem(f"{self.spot_results.SpotData.GetRMSSpotSizeFor(n+1, self.ui.comboBoxSpotWavelength.currentIndex()):.3f}")
                row_items.append(item1)
                # GEO
                item2 = QTableWidgetItem(f"{self.spot_results.SpotData.GetGeoSpotSizeFor(n+1, self.ui.comboBoxSpotWavelength.currentIndex()):.3f}")
                row_items.append(item2)

                self.ui.tableWidgetSpot.insertRow(n)
                for j, item in enumerate(row_items):
                    self.ui.tableWidgetSpot.setItem(n, j, item)

                pass


            # GetRMSSpotSizeFor(int fieldN, int waveN)
            # print('RMS radius: %6.3f  %6.3f  %6.3f' % (
            #     self.spot_results.SpotData.GetRMSSpotSizeFor(1, 1),
            #     self.spot_results.SpotData.GetRMSSpotSizeFor(2, 1),
            #     self.spot_results.SpotData.GetRMSSpotSizeFor(3, 1)))
            # print('GEO radius: %6.3f  %6.3f  %6.3f' % (
            #     self.spot_results.SpotData.GetGeoSpotSizeFor(1, 1),
            #     self.spot_results.SpotData.GetGeoSpotSizeFor(2, 1),
            #     self.spot_results.SpotData.GetGeoSpotSizeFor(3, 1)))

        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算点列图出错：\n{e}")

    def sopt_raytrace(self):
        try:
            self.raytrace = self.TheSystem.Tools.OpenBatchRayTrace()
            nsur = self.TheSystem.LDE.NumberOfSurfaces
            max_rays = self.ui.spinBoxSpotRayDensity.value()
            normUnPolData = self.raytrace.CreateNormUnpol((max_rays + 1) * (max_rays + 1), self.ZOSAPI.Tools.RayTrace.RaysType.Real,
                                                     nsur)
            # Define batch ray trace constants
            hx = 0.0
            max_wave = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            num_fields = self.TheSystem.SystemData.Fields.NumberOfFields
            #hy_ary = np.array([0, 0.707, 1])
            # 计算最大Y视场
            max_field = 0.0
            for i in range(1, num_fields + 1):
                if self.TheSystem.SystemData.Fields.GetField(i).Y > max_field:
                    max_field = self.TheSystem.SystemData.Fields.GetField(i).Y
            #  primary wavelength 主波长
            pwav = 0
            for a in range(1, max_wave + 1):
                if self.TheSystem.SystemData.Wavelengths.GetWavelength(a).IsPrimary == 1:
                    pwav = a
            chief_ary = np.zeros(num_fields)
            # 归一化的Y视场
            hy_ary=[]
            for field in range(1, num_fields + 1):
                hy = 1 if max_field == 0 else self.TheSystem.SystemData.Fields.GetField(field).Y / max_field
                hy_ary.append(hy)
                # gets single value without using MFE (see ZPL OPEV)
                chief_ary[field - 1] = self.TheSystem.MFE.GetOperandValue(self.ZOSAPI.Editors.MFE.MeritOperandType.REAY, nsur,
                                                                     pwav, 0, hy, 0, 0, 0, 0)
            #print(chief_ary)
            #print(hy_ary)
            #hy_ary = np.array([0, 0.707, 1])

            # Initialize x/y image plane arrays
            x_ary = np.empty((num_fields, max_wave, ((max_rays + 1) * (max_rays + 1))))
            y_ary = np.empty((num_fields, max_wave, ((max_rays + 1) * (max_rays + 1))))

            if self.TheSystem.SystemData.Fields.GetFieldType() == self.ZOSAPI.SystemData.FieldType.Angle:
                field_type = 'Angle'
            elif self.TheSystem.SystemData.Fields.GetFieldType() == self.ZOSAPI.SystemData.FieldType.ObjectHeight:
                field_type = 'Height'
            elif self.TheSystem.SystemData.Fields.GetFieldType() == self.ZOSAPI.SystemData.FieldType.ParaxialImageHeight:
                field_type = 'Height'
            elif self.TheSystem.SystemData.Fields.GetFieldType() == self.ZOSAPI.SystemData.FieldType.RealImageHeight:
                field_type = 'Height'

            colors = ('b', 'g', 'r', 'c', 'm', 'y', 'k')
            # all
            self.spot_widget.init_scatter()
            field = self.ui.comboBoxSpotField.currentIndex()+1
            if self.ui.comboBoxSpotWavelength.currentIndex() == 0:
                for wave in range(1, max_wave + 1):
                    # Adding Rays to Batch, varying normalised object height hy
                    normUnPolData.ClearData()
                    waveNumber = wave
                    # for i = 1:((max_rays + 1) * (max_rays + 1))
                    for i in range(1, (max_rays + 1) * (max_rays + 1) + 1):

                        px = np.random.random() * 2 - 1
                        py = np.random.random() * 2 - 1

                        while (px * px + py * py > 1):
                            py = np.random.random() * 2 - 1
                        normUnPolData.AddRay(waveNumber, hx, hy_ary[field - 1], px, py,
                                             Enum.Parse(self.ZOSAPI.Tools.RayTrace.OPDMode, "None"))
                    self.raytrace.RunAndWaitForCompletion()

                    # Read batch raytrace and display results
                    normUnPolData.StartReadingResults()

                    # Python NET requires all arguments to be passed in as reference, so need to have placeholders
                    sysInt = Int32(1)
                    sysDbl = Double(1.0)

                    output = normUnPolData.ReadNextResult(sysInt, sysInt, sysInt,
                                                          sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl,
                                                          sysDbl,
                                                          sysDbl, sysDbl, sysDbl);

                    while output[0]:  # success
                        if ((output[2] == 0) and (output[3] == 0)):  # ErrorCode & vignetteCode
                            x_ary[field - 1, wave - 1, output[1] - 1] = output[4]  # X
                            y_ary[field - 1, wave - 1, output[1] - 1] = output[5]  # Y
                        output = normUnPolData.ReadNextResult(sysInt, sysInt, sysInt,
                                                              sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl, sysDbl,
                                                              sysDbl,
                                                              sysDbl, sysDbl, sysDbl);

                    temp = self.spot_widget.plot_scatter_data(np.squeeze(x_ary[field - 1, wave - 1, :]),
                                    np.squeeze(y_ary[field - 1, wave - 1, :]))
                    print(np.squeeze(x_ary[field - 1, wave - 1, :]),
                                    np.squeeze(y_ary[field - 1, wave - 1, :]))

                pass
            else:
                pass

        except Exception as e:
            QMessageBox.critical(self, "错误", f"光迹追踪出错：\n{e}")

        self.raytrace.Close()
        pass

    # 计算和绘制波前
    def update_wavefront_map(self):
        try:
            self.wavefront_map = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.WavefrontMap)
            # 获取设置API
            wavefront_map_setting = self.wavefront_map.GetSettings()
            # 从UI获取所有的设置参数，进行设置
            current_sampling = self.ui.comboBoxWavefrontMapSampling.currentText()
            if current_sampling == '32x32':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_32x32
            if current_sampling == '64x64':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '128x128':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '256x256':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '512x512':
                wavefront_map_setting.Sampling = self.ZOSAPI.Analysis.SampleSizes.S_512x512

            wavefront_map_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxWavefrontMapWavelength.currentIndex())
            wavefront_map_setting.Field.SetFieldNumber(self.ui.comboBoxWavefrontMapField.currentIndex())
            # 根据更新后的参数重新计算
            self.wavefront_map.ApplyAndWaitForCompletion()
            # 获取计算结果
            self.wavefront_map_result = self.wavefront_map.GetResults()
            self.wavefront_map_values = self.wavefront_map_result.GetDataGrid(0).Values
            self.wavefront_map_npdata = self.zos.reshape(self.wavefront_map_values, self.wavefront_map_values.GetLength(0),
                                 self.wavefront_map_values.GetLength(1))
            # 绘制波前
            self.wavefront_map_widget.plot_grid_image(self.wavefront_map_npdata)
            #print(self.zos.calPV(self.wavefront_map_values))
            # 计算PV RMS
            self.ui.lineEditPV.setText(str(self.zos.calPV(self.wavefront_map_values)))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算波前出错：\n{e}")

    # 计算和绘制MTF，EXAMPLE4
    def update_MTF(self):
        try:
            self.mtf = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.FftMtf)
            mtf_setting = self.mtf.GetSettings()
            current_sampling = self.ui.comboBoxMTFSampling.currentText()
            if current_sampling == '32x32':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_32x32
            if current_sampling == '64x64':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '128x128':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '256x256':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '512x512':
                mtf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512

            mtf_setting.MaximumFrequency = self.ui.spinBoxMTFFrequency.value()
            mtf_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxMTFWavelength.currentIndex())
            mtf_setting.Field.SetFieldNumber(self.ui.comboBoxMTFField.currentIndex())
            self.mtf.ApplyAndWaitForCompletion()

            mtf_result = self.mtf.GetResults()
            self.mtf_widget.init_line_data(self.ui.spinBoxMTFFrequency.value())

            colors = ('b', 'g', 'r', 'c', 'm', 'y', 'k')
            for seriesNum in range(0, mtf_result.NumberOfDataSeries, 1):
                data = mtf_result.GetDataSeries(seriesNum)

                # get raw .NET data into numpy array
                xRaw = data.XData.Data
                yRaw = data.YData.Data

                x = list(xRaw)
                y = self.zos.reshape(yRaw, yRaw.GetLength(0), yRaw.GetLength(1), True)

                self.mtf_widget.plot_line_data(x, y[0], c=colors[seriesNum])
                self.mtf_widget.plot_line_data(x, y[1], ls='--', c=colors[seriesNum])

        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算MTF出错：\n{e}")

    # PSF
    def update_PSF(self):
        try:
            self.psf = self.TheSystem.Analyses.New_Analysis(self.ZOSAPI.Analysis.AnalysisIDM.FftPsf)
            psf_setting = self.psf.GetSettings()
            current_sampling = self.ui.comboBoxPSFSampling.currentText()
            if current_sampling == '32x32':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_32x32
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            if current_sampling == '64x64':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            if current_sampling == '128x128':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            if current_sampling == '256x256':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
            if current_sampling == '512x512':
                psf_setting.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
                psf_setting.OutputSize = self.ZOSAPI.Analysis.SampleSizes.S_1024x1024

            psf_setting.Rotation = self.ui.comboBoxPSFRotation.currentIndex()
            psf_setting.Wavelength.SetWavelengthNumber(self.ui.comboBoxPSFWavelength.currentIndex())
            psf_setting.Field.SetFieldNumber(self.ui.comboBoxPSFField.currentIndex())
            self.psf.ApplyAndWaitForCompletion()

            psf_result = self.psf.GetResults()
            psf_values = psf_result.GetDataGrid(0).Values
            psf_npdata = self.zos.reshape(psf_values,psf_values.GetLength(0),psf_values.GetLength(1))
            self.psf_widget.plot_grid_image(psf_npdata)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算PSF出错：\n{e}")

    # 导入外部zernike文件进行联合仿真
    def addRealZernike(self):
        # 弹出文件对话框
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "select dat file",
            "",
            "DAT Files (*.dat)",
            options=options
        )
        if not file_path:  # 如果未选择文件，返回
            QMessageBox.warning(self, "提示", "未选择任何文件！")
            return
        # 如果选择了文件，解析它
        try:
            with open(file_path, "r") as file:
                lines = file.readlines()

            # 必须是39行
            if len(lines) != 39:
                QMessageBox.critical(self, "错误", "文件格式错误，要求39行！")
                return

            # 读取第一行和第二行
            zernike_num = float(lines[0].strip())
            norm_radius = float(lines[1].strip())

            # 遍历读取剩余的37行
            zernike_coffs = [float(line.strip()) for line in lines[2:]]

            if zernike_num!=37:
                QMessageBox.critical(self, "错误", "文件格式错误，应该使用37项zernike！")
                return

            # 获取当前行数，在tablewidget后进行追加
            row_count = self.ui.tableWidgetRealData.rowCount()
            # 添加新行
            self.ui.tableWidgetRealData.insertRow(row_count)
            # 设置单元格数据，第一列是下拉控件，用来选择对应的镜面
            combo_box = QComboBox()
            combo_box.setToolTip("选择待替换的镜面ID。")
            # 添加所有可选的镜面
            for i in range(0,self.TheSystem.LDE.NumberOfSurfaces):
                combo_box.addItem(str(i))
            self.ui.tableWidgetRealData.setCellWidget(row_count, 0, combo_box)
            self.ui.tableWidgetRealData.setItem(row_count, 1, QTableWidgetItem(str(norm_radius)))
            # 添加37项系数
            for col, value in enumerate(zernike_coffs):
                item = QTableWidgetItem(str(value))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.ui.tableWidgetRealData.setItem(row_count, col+2, item)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"解析文件时出错：\n{e}")

    # 删除选择的zernike，选择整行才能删除
    def delRealZernike(self):
        try:
            # 获取当前选中的行
            selected_indexes = self.ui.tableWidgetRealData.selectedIndexes()
            if not selected_indexes:
                QMessageBox.warning(self, "提示", "未选择任何行！")
                return
            # 从表格中删除选中的行
            self.ui.tableWidgetRealData.removeRow(selected_indexes[0].row())

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除zernike出错：\n{e}")

    # 更新计算
    def updateRealZernike(self):
        try:
            TheLDE = self.zos.TheSystem.LDE
            # 遍历所有要替换的镜子
            for row in range(self.ui.tableWidgetRealData.rowCount()):
                combo_box = self.ui.tableWidgetRealData.cellWidget(row, 0)
                line_edit = self.ui.tableWidgetRealData.cellWidget(row, 1)
                # 获取替换镜子的ID
                surface_id = int(combo_box.currentText())
                # 设置镜子类型
                SurfaceType = TheLDE.GetSurfaceAt(surface_id).GetSurfaceTypeSettings(self.ZOSAPI.Editors.LDE.SurfaceType.ZernikeFringePhase)
                TheLDE.GetSurfaceAt(surface_id).ChangeType(SurfaceType)
                TheLDE.GetSurfaceAt(surface_id).GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par13).Value = str(37)
                for i in range(1,39):
                    TheLDE.GetSurfaceAt(surface_id).GetSurfaceCell(self.ZOSAPI.Editors.LDE.SurfaceColumn.Par13 + i ).Value = self.ui.tableWidgetRealData.item(row, i).text()

                self.update_wavefront_map()
            pass
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新计算报错：\n{e}")

    # EXAMPLE15
    def systemPrediction(self):
        TheLDE = self.TheSystem.LDE
        tools = self.TheSystem.Tools
        tools.RemoveAllVariables()  # 每次预测之前先清空所有变量

        # test = self.TheSystem.Tools.OpenLocalOptimization()
        # print('open opt2:', test)
        # test.Cancel()

        self.str_txt = self.str_txt + f"<p>-----预测轮次: <b>{self.prediction_round}</b>-----</p>"
        self.str_txt = self.str_txt + f"<p>预测前: </p>"
        # 1, 添加变量. 遍历添加控件中的所有变量
        if len(self.prediction_variables) > 0:
            for variable in self.prediction_variables:
                mirror_id = variable.mirror_id
                optimization_item = variable.optimization_item
                OptimizationItemList = list(OptimizationItem)
                try:
                    opt_index = OptimizationItemList.index(optimization_item) + 1
                    self.log_framework.debug("mirror id: {0}. optimization type: {1}".format(mirror_id, optimization_item.name))
                    Surface = TheLDE.GetSurfaceAt(mirror_id)            # 获取镜子
                    self.log_framework.debug(optimization_item)
                    if optimization_item == OptimizationItem.ZDIS:
                        self.log_framework.debug("make thickness solve variable")
                        Surface.ThicknessCell.MakeSolveVariable()
                        variable.last_value = Surface.Thickness
                        self.str_txt = self.str_txt + f"<p>镜片ID: <b>{mirror_id}</b>; 优化类型: 光轴方向间隔</p>"
                        pass
                    elif optimization_item == OptimizationItem.RADIUS:
                        #Surface.FrontRadiusCell.MakeSolveVariable()
                        variable.last_value = Surface.Radius
                        self.str_txt = self.str_txt + f"<p>镜片ID: <b>{mirror_id}</b>; 优化类型: 镜片半径</p>"
                        pass
                    #elif optimization_item.name == OptimizationItem.RADIUS:
                    #    DecenterCell
                    #    pass

                except ValueError:
                    self.log_framework.error("optimization item out of range")

            self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test.zos');
        else:
            self.log_framework.warning("set at last one variable!")
            return

        #2 定义优化函数
        self.TheMFE = self.TheSystem.MFE
        OptWizard = self.TheMFE.SEQOptimizationWizard

        # [TODO] 与zemax的优化配置不一样
        if self.ui.radioButtonPredictionRMS.isChecked():
            self.log_framework.info("using wavefront RMS as prediction object")
            # Optimize for smallest RMS Spot, which is "Data" = 1

            OptWizard.Data = 0      # wavefront
            OptWizard.Type = 0      # 0:RMS, 1:PTV
            OptWizard.OverallWeight = 1
            # Gaussian Quadrature with 3 rings (refers to index number = 2)
            OptWizard.PupilIntegrationMethod = 0
            OptWizard.Ring = 2 # 3 ring
            OptWizard.Arm = 0 # 6 arm
            # Set air & glass boundaries
            # [TODO]
            # And click OK!
            OptWizard.Apply()

            # mf_filename = 'D:/code/gitproject/OpticalRealSim/RMS_Spot_Radius.mf'
            # TheMFE.SaveMeritFunction(mf_filename)
            # TheMFE.LoadMeritFunction(mf_filename)
            # self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test2.zos')

            self.str_txt = self.str_txt + f"<p>优化目标: 波前RMS; </p>"
            pass

        if self.ui.radioButtonPredictionSpot.isChecked():
            self.log_framework.info("using Spot RMS radius as prediction object")
            # Optimize for smallest RMS Spot, which is "Data" = 1
            OptWizard.Data = 1
            OptWizard.Type = 0      #0:RMS, 1:PTV(Geometric)
            OptWizard.OverallWeight = 1
            # Gaussian Quadrature with 3 rings (refers to index number = 2)
            OptWizard.PupilIntegrationMethod = 0
            OptWizard.Ring = 2  # 3 ring
            OptWizard.Arm = 0  # 6 arm
            # Set air & glass boundaries
            # [TODO]
            # And click OK!
            OptWizard.Apply()

            # 保存优化文件
            # mf_filename = 'D:/code/gitproject/OpticalRealSim/RMS_Spot_Radius.mf'
            # TheMFE.SaveMeritFunction(mf_filename)
            # TheMFE.LoadMeritFunction(mf_filename)
            # self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test2.zos')

            self.str_txt = self.str_txt + f"<p>优化目标: 点列图RMS半径; </p>"

            pass

        # 自定义，此时load启用
        if self.ui.radioButtonPredictionCustom.isChecked():
            self.TheMFE.LoadMeritFunction(self.mf_file)
            pass

        # 3 开始优化
        # Run local optimization and measure time
        t = time.time()
        LocalOpt = tools.OpenLocalOptimization()
        print(LocalOpt)
        if (LocalOpt != None):
            LocalOpt.Algorithm = self.ZOSAPI.Tools.Optimization.OptimizationAlgorithm.DampedLeastSquares        # 阻尼最小二乘法
            LocalOpt.Cycles = self.ZOSAPI.Tools.Optimization.OptimizationCycles.Automatic                       # 迭代: 自动
            LocalOpt.NumberOfCores = os.cpu_count() # 可用的cpu数目
            self.log_framework.info('Local Optimization...')
            self.str_txt = self.str_txt + f"<p>采用局部优化，阻尼最小二乘法，CPU数目<b>{os.cpu_count()}</b> </p>"
            self.log_framework.info('Initial objects value: {0}'.format(LocalOpt.InitialMeritFunction))
            self.str_txt = self.str_txt + f"<p>预测前目标函数: <b>{LocalOpt.InitialMeritFunction}</b> </p>"

            LocalOpt.RunAndWaitForCompletion()
            self.log_framework.info('Final objects value: {0}'.format(LocalOpt.CurrentMeritFunction))
            self.str_txt = self.str_txt + f"<p>预测后目标函数: <b>{LocalOpt.CurrentMeritFunction}</b> </p>"
            LocalOpt.Close()

            pass
        else:
            self.log_framework.debug("open local optimization failed")
        pass

        elapsed = time.time() - t
        self.log_framework.info('Time elapsed: ' + str(round(elapsed, 3)) + 's')
        #self.TheSystem.SaveAs('D:/code/gitproject/OpticalRealSim/test3.zos')
        self.str_txt = self.str_txt + f"<p>预测时间: <b>{round(elapsed, 3)}</b> </p>"

        # 获取优化后变量的数值
        for variable in self.prediction_variables:
            mirror_id = variable.mirror_id
            Surface = TheLDE.GetSurfaceAt(mirror_id)
            #print(variable.last_value)
            #print(Surface.Thickness)
            self.str_txt = self.str_txt + f"<p>优化前: <b>{variable.last_value}</b> </p>"
            self.str_txt = self.str_txt + f"<p>优化后: <b>{Surface.Thickness}</b> </p>"
            opt_value = Surface.Thickness - variable.last_value
            opt_value = opt_value * 1000 # 转换为um
            optimization_item = variable.optimization_item
            if optimization_item == OptimizationItem.ZDIS:
                self.str_txt = self.str_txt + f"<p>优化建议: 镜子(ID=<b>{mirror_id}</b>)Z向移动(<b>{round(opt_value,3)}um</b>) </p>"
                pass
            elif optimization_item == OptimizationItem.RADIUS:
                pass

        self.ui.textBrowserPrediction.setHtml(self.str_txt)
        cursor = self.ui.textBrowserPrediction.textCursor()
        cursor.movePosition(cursor.End)
        self.ui.textBrowserPrediction.setTextCursor(cursor)
        self.prediction_round = self.prediction_round + 1

    def addPredictionVariable(self):
        dialog = AddVariableDialog (self.nsur, self)
        if dialog.exec_() == QDialog.Accepted:
            mirror_id, optimization_item = dialog.get_selected_values()
            self.prediction_variables.append(PredictionVariable(mirror_id, optimization_item))
            self.update_prediction_table()
        pass

    def deletePredictionVariable(self):
        selected_rows = set(index.row() for index in self.ui.tableWidgetPedictionVariables.selectedIndexes())
        if selected_rows:
            for row in sorted(selected_rows, reverse=True):
                del self.prediction_variables[row]
                self.ui.tableWidgetPedictionVariables.removeRow(row)
        pass

    def deleteAllPredictionVariable(self):
        self.prediction_variables = []
        self.ui.tableWidgetPedictionVariables.setRowCount(0)
        pass

    def update_prediction_table(self):
        self.ui.tableWidgetPedictionVariables.setRowCount(len(self.prediction_variables))
        self.ui.tableWidgetPedictionVariables.horizontalHeader().setSectionResizeMode(len(self.prediction_variables)-1, QHeaderView.Stretch)
        for row, variable in enumerate(self.prediction_variables):
            mirror_id_item = QTableWidgetItem(str(variable.mirror_id))
            optimization_item_item = QTableWidgetItem(variable.optimization_item.name)
            self.ui.tableWidgetPedictionVariables.setItem(row, 0, mirror_id_item)
            self.ui.tableWidgetPedictionVariables.setItem(row, 1, optimization_item_item)
        pass

    def loadPredictionConfigFile(self):
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_filter = "Optimization file (*.mf);;Merit Function file (*.mf)"  # 根据需求指定文件后缀过滤条件
        file_name, _ = QFileDialog.getOpenFileName(self, "select file", "", file_filter, options=options)
        # 如果读取到了zemax文件，执行操作
        if file_name:
            self.mf_file = file_name
            pass
        else:
            self.log_framework.warning("请选择优化mf文件")
            return
        pass

    def onPredictionRadioChanged(self):
        if self.ui.radioButtonPredictionRMS.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(False)
            pass
        elif self.ui.radioButtonPredictionSpot.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(False)
            pass
        elif self.ui.radioButtonPredictionCustom.isChecked():
            self.ui.pushButtonLoadPredictionConfig.setEnabled(True)
            pass
        pass

    # 公差敏感性分析配置
    def sensitivityConfig(self):
        tWiz = self.TheSystem.TDE.SEQToleranceWizard
        tWiz.IsSurfaceRadiusUsed = False
        tWiz.IsSurfaceThicknessUsed = False
        tWiz.IsSurfaceDecenterXUsed = False
        tWiz.IsSurfaceDecenterYUsed = False
        tWiz.IsSurfaceTiltXUsed = False
        tWiz.IsSurfaceTiltYUsed = False
        tWiz.IsSurfaceSandAIrregularityUsed = False

        tWiz.IsIndexUsed = False
        tWiz.IsIndexAbbePercentageUsed = False

        tWiz.IsFocusCompensationUsed = True

        dialog = SensivitityDialog(self.nsur)
        if dialog.exec_() == QDialog.Accepted:
            
            '''tWiz.IsSurfaceZernikeIrregularityUsed = dialog.zernike_irregularity_check.isChecked()
            tWiz.IsElementDecenterXUsed =dialog.decenterX_check.isChecked()
            tWiz.IsElementDecenterYUsed = dialog.decenterY_check.isChecked()
            tWiz.IsElementTiltXUsed = dialog.tiltX_check.isChecked()
            tWiz.IsElementTiltYUsed = dialog.tiltY_check.isChecked()

            tWiz.StartAtSurface = int(dialog.start_at_surface_combo.currentText())
            tWiz.StopAtSurface = int(dialog.stop_at_surface_combo.currentText())
            if tWiz.IsSurfaceZernikeIrregularityUsed:
                tWiz.SurfaceZernikeIrregularityFringes = float(dialog.fringes_edit.text())

            if tWiz.IsElementDecenterXUsed:
                tWiz.ElementDecenterX = float(dialog.decenterX_edit.text())

            if tWiz.IsElementDecenterYUsed:
                tWiz.ElementDecenterY = float(dialog.decenterY_edit.text())

            if tWiz.IsElementTiltXUsed:
                tWiz.ElementTiltXDegrees = float(dialog.tiltX_edit.text())

            if tWiz.IsElementTiltYUsed:
                tWiz.ElementTiltYDegrees = float(dialog.tiltY_edit.text())

            tWiz.TestWavelength = float(dialog.test_wavelength_edit.text())
            print(float(dialog.test_wavelength_edit.text()))
            pass

        tWiz.OK()'''

        # setup
        self.tol = self.TheSystem.Tools.OpenTolerancing()
        # Select Sensitivity mode
        self.tol.SetupMode = self.ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity

        # Select Criterion and related settings
        print(dialog.criterion_combo.currentText())
        if dialog.criterion_combo.currentText() == "RMS Spot Radius":
            self.tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
            pass
        elif dialog.criterion_combo.currentText() == "RMS Wavefront":
            self.tol.Criterion = self.ZOSAPI.Tools.Tolerancing.Criterions.RMSWavefront
            pass
        self.tol.CriterionSampling = int(dialog.sampling_combo.currentText())
        self.tol.CriterionComp = self.ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
        self.tol.CriterionCycle = 2
        #self.tol.SetupCore = os.cpu_count()
        self.tol.CriterionField = self.ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined
        # Select number of MC runs and files to save
        self.tol.NumberOfRuns = dialog.monte_carlo_spinbox.value()
        #self.tol.NumberToSave = 20

        pass

    # EXAMPLE14 公差分析：SEQToleranceWizard  OpenTolerancing (ITolerancing)
    def sensitivityAnalysis(self):
        t = time.time()
        # Run the Tolerancing analysis
        self.tol.RunAndWaitForCompletion()
        self.tol.Close()
        elapsed = time.time() - t
        self.log_framework.info('Sensitivity analysis time elapsed: ' + str(round(elapsed, 3)) + 's')

        self.updateSensitivityResultUI()

        pass

    def updateSensitivityResultUI(self):
        self.ui.textBrowserSensitivity.clear()
        # 打开数据窗口，获取数据
        tolDataView = self.TheSystem.Tools.OpenToleranceDataViewer()
        tolDataView.RunAndWaitForCompletion()
        SData = tolDataView.SensitivityData
        # 敏感性的：分析标准、补偿类型、对应的操作数的数目
        print('NumberOfCriteria: ', SData.NumberOfCriteria)
        print('NumberOfCompensators: ', SData.NumberOfCompensators)
        print('NumberOfResultOperands: ', SData.NumberOfResultOperands)

        self.ui.textBrowserSensitivity.append("分析元素数目:" + str(SData.NumberOfResultOperands))

        sdc = SensitivityDataContainer()

        for i in range(SData.NumberOfResultOperands):
            self.ui.textBrowserSensitivity.append('type: ' + self.getSensitivityOperandName(SData.GetOperand(i).OperandType) + ' Minimum: ' + str(SData.GetOperand(i).Minimum) +' Maximum: ' + str(SData.GetOperand(i).Maximum) + ' min: ' + str(SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum) +' max: ' + str(SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum))
            #print('Maximum: ', SData.GetOperand(i).Maximum)
            #print('min: ', SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum)
            #print('max: ', SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum)
            sdc.append_data(operand_type = self.getSensitivityOperandName(SData.GetOperand(i).OperandType), operand_value = SData.GetOperand(i).Minimum, operand_change = SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMinimum)
            sdc.append_data(self.getSensitivityOperandName(SData.GetOperand(i).OperandType), SData.GetOperand(i).Maximum, SData.GetOperand(i).GetEffectOnCriterion(0).EstimatedChangeMaximum)
            pass
        sdc.sort_data_descending()
        for data in sdc.data_list:
             self.ui.textBrowserSensitivity.append(
                 f'Type: {data.operand_type}, Value: {data.operand_value}, Change: {data.operand_change}')

        sdc.clear_data()
        tolDataView.Close()
        pass

    def getSensitivityOperandName(self, n):
        if n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEDX:
            return 'TEDX'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEDY:
            return 'TEDY'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TETX:
            return 'TETX'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TETY:
            return 'TETY'
        elif n == self.ZOSAPI.Tools.Tolerancing.TolerancingOperand.TEZI:
            return 'TEZI'