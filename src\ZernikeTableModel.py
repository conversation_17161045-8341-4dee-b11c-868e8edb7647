import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QFileDialog, QMessageBox,
    QTableView, QHBoxLayout, QStyledItemDelegate, QComboBox, QLineEdit
)
from PyQt5.QtCore import QAbstractTableModel, Qt

class ComboBoxDelegate(QStyledItemDelegate):
    def __init__(self, items, parent=None):
        super().__init__(parent)
        self.items = items

    def createEditor(self, parent, option, index):
        editor = QComboBox(parent)
        editor.addItems(self.items)
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.DisplayRole)
        editor.setCurrentText(value)

    def setModelData(self, editor, model, index):
        model.setData(index, editor.currentText(), Qt.EditRole)


class LineEditDelegate(QStyledItemDelegate):
    def createEditor(self, parent, option, index):
        editor = QLineEdit(parent)
        return editor

    def setEditorData(self, editor, index):
        value = index.data(Qt.DisplayRole)
        editor.setText(value)

    def setModelData(self, editor, model, index):
        model.setData(index, editor.text(), Qt.EditRole)


class ZernikeTableModel(QAbstractTableModel):
    def __init__(self):
        super().__init__()
        self.data_list = []  # 数据
        self.headers = ["name", "normal_radius"] + [str(i) for i in range(1,38)]  # 表头

    def rowCount(self, parent=None):
        return len(self.data_list)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role):
        if role == Qt.DisplayRole:  # 显示数据
            return self.data_list[index.row()][index.column()]

    def setData(self, index, value, role):
        if role == Qt.EditRole:
            self.data_list[index.row()][index.column()] = value
            self.dataChanged.emit(index, index, [role])
            return True
        return False

    def flags(self, index):
        if index.column() == 1:  # 第二列可编辑
            return Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable
        return Qt.ItemIsSelectable | Qt.ItemIsEnabled

    def headerData(self, section, orientation, role):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:  # 列标题
                return self.headers[section]
            elif orientation == Qt.Vertical:  # 行标题
                return section + 1  # 行号从1开始

    def addRow(self, row_data):
        """向模型中添加一行"""
        self.beginInsertRows(self.index(len(self.data_list), 0).parent(), len(self.data_list), len(self.data_list))
        self.data_list.append(row_data)
        self.endInsertRows()

    def removeRow(self, row, parent=None):
        """从模型中删除指定行"""
        if 0 <= row < len(self.data_list):
            self.beginRemoveRows(parent or self.index(row, 0).parent(), row, row)
            self.data_list.pop(row)  # 删除数据
            self.endRemoveRows()
            return True
        return False