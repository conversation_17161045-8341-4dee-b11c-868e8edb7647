from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QTextBrowser
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QTextCursor, QColor

# how to use
# self.log_display = LogDisplay(self.text_browser)
# self.log_framework = LogFramework(self.log_display)
# self.log_framework.debug("This is a debug message.")
# self.log_framework.info("This is an info message.")
# self.log_framework.warning("This is a warning message.")
# self.log_framework.error("This is an error message.")

class LogRecorder(QObject):
    logRecorded = pyqtSignal(str, str)

    def __init__(self):
        super().__init__()

    def record(self, log_level, message):
        log_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        log_message = f"[{log_time}] [{log_level}] {message}"
        self.logRecorded.emit(log_message, log_level)

class LogDisplay:
    def __init__(self, text_browser: QTextBrowser):
        self.text_browser = text_browser
        self.text_browser.setReadOnly(True)
        self.text_browser.setStyleSheet("background-color: #f0f0f0; color: #333333;")

    def appendLog(self, log_message, log_level):
        color = self.getColorForLogLevel(log_level)
        html_message = f'<font color="{color}">{log_message}</font><br>'
        self.text_browser.moveCursor(QTextCursor.End)
        self.text_browser.insertHtml(html_message)
        self.text_browser.ensureCursorVisible()

    def getColorForLogLevel(self, log_level):
        if log_level == "DEBUG":
            return "#888888"  # 灰色
        elif log_level == "INFO":
            return "#000000"  # 黑色
        elif log_level == "WARNING":
            return "#ffa500"  # 橙色
        elif log_level == "ERROR":
            return "#ff0000"  # 红色
        else:
            return "#000000"  # 默认颜色

class LogFramework:
    def __init__(self, log_display: LogDisplay):
        self.log_display = log_display
        self.log_recorder = LogRecorder()
        self.log_recorder.logRecorded.connect(self.log_display.appendLog)

    def debug(self, message):
        self.log_recorder.record("DEBUG", message)

    def info(self, message):
        self.log_recorder.record("INFO", message)

    def warning(self, message):
        self.log_recorder.record("WARNING", message)

    def error(self, message):
        self.log_recorder.record("ERROR", message)

