import sys
from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QTableWidget, QTableWidgetItem, QVBoxLayout, QHBoxLayout, QMessageBox, QInputDialog
from enum import Enum

class OptimizationItem(Enum):
    ITEM1 = 1
    ITEM2 = 2
    ITEM3 = 3

class PredictionVariable:
    def __init__(self, mirror_id: int, optimization_item: OptimizationItem):
        self.mirror_id = mirror_id
        self.optimization_item = optimization_item

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Prediction Variable Manager")
        self.setGeometry(100, 100, 400, 300)

        self.prediction_variables = []

        self.add_button = QPushButton("Add")
        self.add_button.clicked.connect(self.add_prediction_variable)

        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.delete_prediction_variable)

        self.delete_all_button = QPushButton("Delete All")
        self.delete_all_button.clicked.connect(self.delete_all_prediction_variables)

        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(2)
        self.table_widget.setHorizontalHeaderLabels(["Mirror ID", "Optimization Item"])

        layout = QVBoxLayout()
        layout.addWidget(self.table_widget)

        button_layout = QHBoxLayout()
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.delete_all_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def add_prediction_variable(self):
        mirror_id, ok = QInputDialog.getInt(self, "Add Prediction Variable", "Enter Mirror ID:")
        if ok:
            optimization_item, ok = QInputDialog.getItem(self, "Add Prediction Variable", "Select Optimization Item:", [item.name for item in OptimizationItem], editable=False)
            if ok:
                self.prediction_variables.append(PredictionVariable(mirror_id, OptimizationItem[optimization_item]))
                self.update_table()

    def delete_prediction_variable(self):
        selected_rows = set(index.row() for index in self.table_widget.selectedIndexes())
        if selected_rows:
            for row in sorted(selected_rows, reverse=True):
                del self.prediction_variables[row]
                self.table_widget.removeRow(row)

    def delete_all_prediction_variables(self):
        self.prediction_variables = []
        self.table_widget.setRowCount(0)

    def update_table(self):
        self.table_widget.setRowCount(len(self.prediction_variables))
        for row, variable in enumerate(self.prediction_variables):
            mirror_id_item = QTableWidgetItem(str(variable.mirror_id))
            optimization_item_item = QTableWidgetItem(variable.optimization_item.name)
            self.table_widget.setItem(row, 0, mirror_id_item)
            self.table_widget.setItem(row, 1, optimization_item_item)

app = QApplication(sys.argv)
window = MainWindow()
window.show()
sys.exit(app.exec_())
