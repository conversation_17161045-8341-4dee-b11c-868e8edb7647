import clr, os, winreg
from itertools import islice


class PythonStandaloneApplication(object):
    class LicenseException(Exception):
        pass

    class ConnectionException(Exception):
        pass

    class InitializationException(Exception):
        pass

    class SystemNotPresentException(Exception):
        pass

    def __init__(self, path=None):
        # determine location of ZOSAPI_NetHelper.dll & add as reference
        aKey = winreg.OpenKey(winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), r"Software\Zemax", 0,
                              winreg.KEY_READ)
        zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
        NetHelper = os.path.join(os.sep, zemaxData[0], r'ZOS-API\Libraries\ZOSAPI_NetHelper.dll')
        winreg.CloseKey(aKey)
        clr.AddReference(NetHelper)
        import ZOSAPI_NetHelper

        # Find the installed version of OpticStudio
        if path is None:
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize()
        else:
            # Note -- uncomment the following line to use a custom initialization path
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize(path)

        # determine the ZOS root directory
        if isInitialized:
            dir = ZOSAPI_NetHelper.ZOSAPI_Initializer.GetZemaxDirectory()
        else:
            raise PythonStandaloneApplication.InitializationException(
                "Unable to locate Zemax OpticStudio.  Try using a hard-coded path.")

        # add ZOS-API referencecs
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI.dll"))
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI_Interfaces.dll"))
        import ZOSAPI

        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI

        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI

        # Create the initial connection class
        self.TheConnection = ZOSAPI.ZOSAPI_Connection()

        if self.TheConnection is None:
            raise PythonStandaloneApplication.ConnectionException("Unable to initialize .NET connection to ZOSAPI")

        self.TheApplication = self.TheConnection.CreateNewApplication()
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("Unable to acquire ZOSAPI application")

        if self.TheApplication.IsValidLicenseForAPI == False:
            raise PythonStandaloneApplication.LicenseException("License is not valid for ZOSAPI use")

        self.TheSystem = self.TheApplication.PrimarySystem
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")

    def __del__(self):
        if self.TheApplication is not None:
            self.TheApplication.CloseApplication()
            self.TheApplication = None

        self.TheConnection = None

    def OpenFile(self, filepath, saveIfNeeded):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.LoadFile(filepath, saveIfNeeded)

    def CloseFile(self, save):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("Unable to acquire Primary system")
        self.TheSystem.Close(save)

    def SamplesDir(self):
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("Unable to acquire ZOSAPI application")

        return self.TheApplication.SamplesDir

    def ExampleConstants(self):
        if self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusType.PremiumEdition:
            return "Premium"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeProfessionalEdition:
            return "Professional"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeStandardEdition:
            return "Standard"
        else:
            return "Invalid"

    def reshape(self, data, x, y, transpose=False):
        """Converts a System.Double[,] to a 2D list for plotting or post processing

        Parameters
        ----------
        data      : System.Double[,] data directly from ZOS-API
        x         : x width of new 2D list [use var.GetLength(0) for dimension]
        y         : y width of new 2D list [use var.GetLength(1) for dimension]
        transpose : transposes data; needed for some multi-dimensional line series data

        Returns
        -------
        res       : 2D list; can be directly used with Matplotlib or converted to
                    a numpy array using numpy.asarray(res)
        """
        if type(data) is not list:
            data = list(data)
        var_lst = [y] * x;
        it = iter(data)
        res = [list(islice(it, i)) for i in var_lst]
        if transpose:
            return self.transpose(res);
        return res

    def transpose(self, data):
        """Transposes a 2D list (Python3.x or greater).

        Useful for converting mutli-dimensional line series (i.e. FFT PSF)

        Parameters
        ----------
        data      : Python native list (if using System.Data[,] object reshape first)

        Returns
        -------
        res       : transposed 2D list
        """
        if type(data) is not list:
            data = list(data)
        return list(map(list, zip(*data)))


if __name__ == '__main__':
    zos = PythonStandaloneApplication()

    # load local variables
    ZOSAPI = zos.ZOSAPI
    TheApplication = zos.TheApplication
    TheSystem = zos.TheSystem
    if not os.path.exists(TheApplication.SamplesDir + "\\API\\Python"):
        os.makedirs(TheApplication.SamplesDir + "\\API\\Python")

    # ! [e14s01_py]
    # Open Double Gauss sample file
    samplesFolder = TheApplication.SamplesDir
    DGfile = samplesFolder + r"\Sequential\Objectives\Double Gauss 28 degree field.zos"
    TheSystem.LoadFile(DGfile, False)
    # ! [e14s01_py]

    # ! [e14s02_py]
    # Set up Tolerance Wizard and run it
    tWiz = TheSystem.TDE.SEQToleranceWizard

    # Specify surface tolerances
    tWiz.SurfaceRadius = 0.1
    tWiz.SurfaceThickness = 0.1
    tWiz.SurfaceDecenterX = 0.1
    tWiz.SurfaceDecenterY = 0.1
    tWiz.SurfaceTiltX = 0.2
    tWiz.SurfaceTiltY = 0.2
    # Specify element tolerances
    tWiz.ElementDecenterX = 0.1
    tWiz.ElementDecenterY = 0.1
    tWiz.ElementTiltXDegrees = 0.2
    tWiz.ElementTiltYDegrees = 0.2
    # Specify tolerances not to be used
    tWiz.IsSurfaceSandAIrregularityUsed = False
    tWiz.IsIndexUsed = False
    tWiz.IsIndexAbbePercentageUsed = False
    print('start surface: ', tWiz.StartAtSurface, 'stop surface: ', tWiz.StopAtSurface)
    tWiz.OK()
    # ! [e14s02_py]

    # ! [e14s03_py]
    # Create a "Double Gauss" folder in the Samples folder
    import os

    dirLoc = samplesFolder + "\\API\\Python\\e14_seq_tolerance"
    if not os.path.exists(dirLoc):
        os.makedirs(dirLoc)
    # Save new file to Double Gauss folder
    fileNameSeq = dirLoc + "\\Double Gauss (seq).zos"
    TheSystem.SaveAs(fileNameSeq)
    # ! [e14s03_py]
    print('File saved: %s' % fileNameSeq)

    # ! [e14s04_py]
    # Set up Tolerancing analysis and run it
    tol = TheSystem.Tools.OpenTolerancing()
    # Select Sensitivity mode
    tol.SetupMode = ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity
    # Select Criterion and related settings
    tol.Criterion = ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
    tol.CriterionSampling = 3
    tol.CriterionComp = ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
    tol.CriterionCycle = 2
    tol.CriterionField = ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined
    # Select number of MC runs and files to save
    tol.NumberOfRuns = 20
    tol.NumberToSave = 20
    tol.OutputFile = dirLoc + "\\Double Gauss (seq).ztd"
    tol.SaveTolDataFile = True
    tol.TolDataFile = dirLoc + "\\Double Gauss (seq).ztd"
    print(tol.ResultFilename)
    # Run the Tolerancing analysis
    tol.RunAndWaitForCompletion()
    tol.Close()
    # ! [e14s04_py]

    # ! [e14s05_py]
    # # Convert file to Non-sequential mode
    # convertNSmode = TheSystem.Tools.OpenConvertToNSCGroup()
    # convertNSmode.ConvertFileToNSC = True
    # convertNSmode.RunAndWaitForCompletion()
    # convertNSmode.Close();
    # # Save the Non-sequential file to the Double Gauss folder
    # fileNameNS = dirLoc + "\\Double Gauss (NS).zos"
    # TheSystem.SaveAs(fileNameNS)
    # # ! [e14s05_py]
    # print('File saved: %s' % fileNameNS)

    fileNameNS = dirLoc + "\\Double Gauss (seq) TOL.zos"
    TheSystem.SaveAs(dirLoc + "\\Double Gauss (seq) TOL.zos")
    print('File saved: %s' % fileNameNS)

    tolDataView = TheSystem.Tools.OpenToleranceDataViewer()
    tolDataView.RunAndWaitForCompletion()
    MCData = tolDataView.MonteCarloData
    MCDataValues = MCData.Values.Rows
    print(MCDataValues)

    SData = tolDataView.SensitivityData
    # 敏感性的：分析标准、补偿类型、对应的操作数的数目
    print('NumberOfCriteria: ', SData.NumberOfCriteria)
    print('NumberOfCompensators: ', SData.NumberOfCompensators)
    print('NumberOfResultOperands: ', SData.NumberOfResultOperands)
    # 获取第0个分析标准
    print('name: ',SData.GetCriterion(0).Name, 'nominal value: ',SData.GetCriterion(0).NominalValue, 'num of operands: ', SData.GetCriterion(0).NumberOfOperands)
    print(ZOSAPI.Tools.Tolerancing.Criterions.RMSWavefront )
    # 分析标准的对应操作数0,1,2,.. 对应summary中的Sensitivity Analysis:字段
    # 字段中的Value 表示标量的变化值，也就是公差
    # EstimatedChangeMinimum + NominalValue = 字段中的 Criterion
    # 字段中的Change，EstimatedChangeMinimum  EstimatedChangeMaximum
    print('criterion 0, effect by operand 0, min:', SData.GetCriterion(0).GetEffectByOperand(0).EstimatedChangeMinimum)
    print('criterion 0, effect by operand 0, max:', SData.GetCriterion(0).GetEffectByOperand(0).EstimatedChangeMaximum)

    # 补偿器, 对应summary中的Compensator Statistics字段
    print('Compensator:  minimum: ', SData.GetCompensator(0).Minimum, 'maximum: ', SData.GetCompensator(0).Maximum, 'mean: ',SData.GetCompensator(0).Mean, 'deviation: ', SData.GetCompensator(0).PopulationStandardDeviation, 'deviation2: ', SData.GetCompensator(0).SampleStandardDeviation)

    # 操作数 的公差取值范围， 父类OperandType， 祖类Name SummaryStatistics等 敏感性计算时的最小最大
    print('operand0: min: ', SData.GetOperand(0).Minimum, 'max: ', SData.GetOperand(0).Maximum, 'comment: ', SData.GetOperand(0).Comment, 'num of criteria: ', SData.GetOperand(0).NumberOfCriteria, 'type: ',SData.GetOperand(0).OperandType, 'name: ', SData.GetOperand(0).Name, 'num of parameters: ', SData.GetOperand(0).NumberOfParameters, 'SummaryStatistics.Minimum ', SData.GetOperand(0).SummaryStatistics.Minimum,'SummaryStatistics.Maximum ', SData.GetOperand(0).SummaryStatistics.Maximum,'SummaryStatistics.Mean ', SData.GetOperand(0).SummaryStatistics.Mean )
    print('operand1: min: ', SData.GetOperand(1).Minimum, 'max: ', SData.GetOperand(1).Maximum, 'comment: ', SData.GetOperand(1).Comment, 'num of criteria: ', SData.GetOperand(1).NumberOfCriteria)
    print('rows: ', SData.GetOperand(0).AsTeziData())

    print('tolDataView.Summary: ', tolDataView.Summary)


    # This will clean up the connection to OpticStudio.
    # Note that it closes down the server instance of OpticStudio, so you for maximum performance do not do
    # this until you need to.
    del zos
    zos = None
