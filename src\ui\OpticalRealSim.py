# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src/ui/OpticalRealSim.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1183, 821)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout_19 = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout_19.setObjectName("verticalLayout_19")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButtonOpenZemax = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonOpenZemax.sizePolicy().hasHeightForWidth())
        self.pushButtonOpenZemax.setSizePolicy(sizePolicy)
        self.pushButtonOpenZemax.setObjectName("pushButtonOpenZemax")
        self.horizontalLayout.addWidget(self.pushButtonOpenZemax)
        self.label = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.lineEditZemaxFilename = QtWidgets.QLineEdit(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lineEditZemaxFilename.sizePolicy().hasHeightForWidth())
        self.lineEditZemaxFilename.setSizePolicy(sizePolicy)
        self.lineEditZemaxFilename.setObjectName("lineEditZemaxFilename")
        self.horizontalLayout.addWidget(self.lineEditZemaxFilename)
        self.verticalLayout_19.addLayout(self.horizontalLayout)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.verticalLayout_18 = QtWidgets.QVBoxLayout()
        self.verticalLayout_18.setObjectName("verticalLayout_18")
        self.groupBoxSystemData = QtWidgets.QGroupBox(self.centralwidget)
        self.groupBoxSystemData.setObjectName("groupBoxSystemData")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBoxSystemData)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tableWidgetSystemData = QtWidgets.QTableWidget(self.groupBoxSystemData)
        self.tableWidgetSystemData.setObjectName("tableWidgetSystemData")
        self.tableWidgetSystemData.setColumnCount(0)
        self.tableWidgetSystemData.setRowCount(0)
        self.verticalLayout.addWidget(self.tableWidgetSystemData)
        self.verticalLayout_18.addWidget(self.groupBoxSystemData)
        self.groupBoxLensData = QtWidgets.QGroupBox(self.centralwidget)
        self.groupBoxLensData.setObjectName("groupBoxLensData")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBoxLensData)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.tableWidgetLensData = QtWidgets.QTableWidget(self.groupBoxLensData)
        self.tableWidgetLensData.setObjectName("tableWidgetLensData")
        self.tableWidgetLensData.setColumnCount(0)
        self.tableWidgetLensData.setRowCount(0)
        self.verticalLayout_2.addWidget(self.tableWidgetLensData)
        self.verticalLayout_18.addWidget(self.groupBoxLensData)
        self.groupBox = QtWidgets.QGroupBox(self.centralwidget)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.pushButtonAdd = QtWidgets.QPushButton(self.groupBox)
        self.pushButtonAdd.setObjectName("pushButtonAdd")
        self.horizontalLayout_2.addWidget(self.pushButtonAdd)
        self.pushButtonDelete = QtWidgets.QPushButton(self.groupBox)
        self.pushButtonDelete.setObjectName("pushButtonDelete")
        self.horizontalLayout_2.addWidget(self.pushButtonDelete)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.pushButtonUpdate = QtWidgets.QPushButton(self.groupBox)
        self.pushButtonUpdate.setObjectName("pushButtonUpdate")
        self.horizontalLayout_2.addWidget(self.pushButtonUpdate)
        self.verticalLayout_7.addLayout(self.horizontalLayout_2)
        self.tableWidgetRealData = QtWidgets.QTableWidget(self.groupBox)
        self.tableWidgetRealData.setObjectName("tableWidgetRealData")
        self.tableWidgetRealData.setColumnCount(0)
        self.tableWidgetRealData.setRowCount(0)
        self.verticalLayout_7.addWidget(self.tableWidgetRealData)
        self.verticalLayout_18.addWidget(self.groupBox)
        
        # 设置各控件的伸缩比例
        self.verticalLayout_18.setStretch(0, 1)  # SystemData
        self.verticalLayout_18.setStretch(1, 3)  # LensData - 扩大显示比例
        self.verticalLayout_18.setStretch(2, 1)  # RealDataAnalysis
        
        self.horizontalLayout_8.addLayout(self.verticalLayout_18)
        self.tabWidget = QtWidgets.QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName("tabWidget")
        self.tabInspection = QtWidgets.QWidget()
        self.tabInspection.setObjectName("tabInspection")
        self.tabWidget.addTab(self.tabInspection, "")
        self.tabSpotDiagram = QtWidgets.QWidget()
        self.tabSpotDiagram.setObjectName("tabSpotDiagram")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.tabSpotDiagram)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.groupBoxSpotDiagram = QtWidgets.QGroupBox(self.tabSpotDiagram)
        self.groupBoxSpotDiagram.setObjectName("groupBoxSpotDiagram")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBoxSpotDiagram)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.gridLayout_2 = QtWidgets.QGridLayout()
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.label_9 = QtWidgets.QLabel(self.groupBoxSpotDiagram)
        self.label_9.setObjectName("label_9")
        self.gridLayout_2.addWidget(self.label_9, 0, 0, 1, 1)
        self.comboBoxSpotWavelength = QtWidgets.QComboBox(self.groupBoxSpotDiagram)
        self.comboBoxSpotWavelength.setObjectName("comboBoxSpotWavelength")
        self.gridLayout_2.addWidget(self.comboBoxSpotWavelength, 0, 3, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.groupBoxSpotDiagram)
        self.label_8.setObjectName("label_8")
        self.gridLayout_2.addWidget(self.label_8, 0, 2, 1, 1)
        self.spinBoxSpotRayDensity = QtWidgets.QSpinBox(self.groupBoxSpotDiagram)
        self.spinBoxSpotRayDensity.setObjectName("spinBoxSpotRayDensity")
        self.gridLayout_2.addWidget(self.spinBoxSpotRayDensity, 0, 1, 1, 1)
        self.label_10 = QtWidgets.QLabel(self.groupBoxSpotDiagram)
        self.label_10.setObjectName("label_10")
        self.gridLayout_2.addWidget(self.label_10, 1, 2, 1, 1)
        self.comboBoxSpotField = QtWidgets.QComboBox(self.groupBoxSpotDiagram)
        self.comboBoxSpotField.setObjectName("comboBoxSpotField")
        self.gridLayout_2.addWidget(self.comboBoxSpotField, 1, 3, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.groupBoxSpotDiagram)
        self.label_11.setObjectName("label_11")
        self.gridLayout_2.addWidget(self.label_11, 1, 0, 1, 1)
        self.comboBoxSpotReferTo = QtWidgets.QComboBox(self.groupBoxSpotDiagram)
        self.comboBoxSpotReferTo.setObjectName("comboBoxSpotReferTo")
        self.gridLayout_2.addWidget(self.comboBoxSpotReferTo, 1, 1, 1, 1)
        self.verticalLayout_3.addLayout(self.gridLayout_2)
        self.splitter_3 = QtWidgets.QSplitter(self.groupBoxSpotDiagram)
        self.splitter_3.setOrientation(QtCore.Qt.Horizontal)
        self.splitter_3.setObjectName("splitter_3")
        self.widget = QtWidgets.QWidget(self.splitter_3)
        self.widget.setObjectName("widget")
        self.tableWidgetSpot = QtWidgets.QTableWidget(self.splitter_3)
        self.tableWidgetSpot.setObjectName("tableWidgetSpot")
        self.tableWidgetSpot.setColumnCount(0)
        self.tableWidgetSpot.setRowCount(0)
        self.verticalLayout_3.addWidget(self.splitter_3)
        self.verticalLayout_8.addWidget(self.groupBoxSpotDiagram)
        self.tabWidget.addTab(self.tabSpotDiagram, "")
        self.tabWavefrontMap = QtWidgets.QWidget()
        self.tabWavefrontMap.setObjectName("tabWavefrontMap")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.tabWavefrontMap)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.groupBoxWavefrontMap = QtWidgets.QGroupBox(self.tabWavefrontMap)
        self.groupBoxWavefrontMap.setObjectName("groupBoxWavefrontMap")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.groupBoxWavefrontMap)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.comboBoxWavefrontMapSampling = QtWidgets.QComboBox(self.groupBoxWavefrontMap)
        self.comboBoxWavefrontMapSampling.setObjectName("comboBoxWavefrontMapSampling")
        self.gridLayout.addWidget(self.comboBoxWavefrontMapSampling, 0, 1, 1, 1)
        self.label_4 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 1, 0, 1, 1)
        self.comboBoxWavefrontMapField = QtWidgets.QComboBox(self.groupBoxWavefrontMap)
        self.comboBoxWavefrontMapField.setObjectName("comboBoxWavefrontMapField")
        self.gridLayout.addWidget(self.comboBoxWavefrontMapField, 1, 3, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 2, 2, 1, 1)
        self.comboBoxWavefrontMapRotation = QtWidgets.QComboBox(self.groupBoxWavefrontMap)
        self.comboBoxWavefrontMapRotation.setObjectName("comboBoxWavefrontMapRotation")
        self.gridLayout.addWidget(self.comboBoxWavefrontMapRotation, 1, 1, 1, 1)
        self.comboBoxWavefrontMapWavelength = QtWidgets.QComboBox(self.groupBoxWavefrontMap)
        self.comboBoxWavefrontMapWavelength.setObjectName("comboBoxWavefrontMapWavelength")
        self.gridLayout.addWidget(self.comboBoxWavefrontMapWavelength, 0, 3, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 0, 2, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 1, 2, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBoxWavefrontMap)
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 2, 0, 1, 1)
        self.lineEditPV = QtWidgets.QLineEdit(self.groupBoxWavefrontMap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lineEditPV.sizePolicy().hasHeightForWidth())
        self.lineEditPV.setSizePolicy(sizePolicy)
        self.lineEditPV.setObjectName("lineEditPV")
        self.gridLayout.addWidget(self.lineEditPV, 2, 1, 1, 1)
        self.lineEditRMS = QtWidgets.QLineEdit(self.groupBoxWavefrontMap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lineEditRMS.sizePolicy().hasHeightForWidth())
        self.lineEditRMS.setSizePolicy(sizePolicy)
        self.lineEditRMS.setObjectName("lineEditRMS")
        self.gridLayout.addWidget(self.lineEditRMS, 2, 3, 1, 1)
        self.gridLayout.setColumnStretch(3, 1)
        self.verticalLayout_6.addLayout(self.gridLayout)
        self.frame = QtWidgets.QFrame(self.groupBoxWavefrontMap)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.verticalLayout_6.addWidget(self.frame)
        self.verticalLayout_10.addWidget(self.groupBoxWavefrontMap)
        self.tabWidget.addTab(self.tabWavefrontMap, "")
        self.tabMTF = QtWidgets.QWidget()
        self.tabMTF.setObjectName("tabMTF")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.tabMTF)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.groupBoxMTF = QtWidgets.QGroupBox(self.tabMTF)
        self.groupBoxMTF.setObjectName("groupBoxMTF")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.groupBoxMTF)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.gridLayout_3 = QtWidgets.QGridLayout()
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.comboBoxMTFSampling = QtWidgets.QComboBox(self.groupBoxMTF)
        self.comboBoxMTFSampling.setObjectName("comboBoxMTFSampling")
        self.gridLayout_3.addWidget(self.comboBoxMTFSampling, 0, 1, 1, 1)
        self.comboBoxMTFWavelength = QtWidgets.QComboBox(self.groupBoxMTF)
        self.comboBoxMTFWavelength.setObjectName("comboBoxMTFWavelength")
        self.gridLayout_3.addWidget(self.comboBoxMTFWavelength, 0, 3, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.groupBoxMTF)
        self.label_13.setObjectName("label_13")
        self.gridLayout_3.addWidget(self.label_13, 0, 2, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.groupBoxMTF)
        self.label_12.setObjectName("label_12")
        self.gridLayout_3.addWidget(self.label_12, 0, 0, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.groupBoxMTF)
        self.label_14.setObjectName("label_14")
        self.gridLayout_3.addWidget(self.label_14, 1, 0, 1, 1)
        self.spinBoxMTFFrequency = QtWidgets.QSpinBox(self.groupBoxMTF)
        self.spinBoxMTFFrequency.setObjectName("spinBoxMTFFrequency")
        self.gridLayout_3.addWidget(self.spinBoxMTFFrequency, 1, 1, 1, 1)
        self.label_15 = QtWidgets.QLabel(self.groupBoxMTF)
        self.label_15.setObjectName("label_15")
        self.gridLayout_3.addWidget(self.label_15, 1, 2, 1, 1)
        self.comboBoxMTFField = QtWidgets.QComboBox(self.groupBoxMTF)
        self.comboBoxMTFField.setObjectName("comboBoxMTFField")
        self.gridLayout_3.addWidget(self.comboBoxMTFField, 1, 3, 1, 1)
        self.verticalLayout_5.addLayout(self.gridLayout_3)
        self.widgetMTF = QtWidgets.QWidget(self.groupBoxMTF)
        self.widgetMTF.setObjectName("widgetMTF")
        self.verticalLayout_5.addWidget(self.widgetMTF)
        self.verticalLayout_11.addWidget(self.groupBoxMTF)
        self.tabWidget.addTab(self.tabMTF, "")
        self.tabPSF = QtWidgets.QWidget()
        self.tabPSF.setObjectName("tabPSF")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.tabPSF)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.groupBoxPSF = QtWidgets.QGroupBox(self.tabPSF)
        self.groupBoxPSF.setObjectName("groupBoxPSF")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBoxPSF)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.gridLayout_4 = QtWidgets.QGridLayout()
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_16 = QtWidgets.QLabel(self.groupBoxPSF)
        self.label_16.setObjectName("label_16")
        self.gridLayout_4.addWidget(self.label_16, 0, 0, 1, 1)
        self.comboBoxPSFWavelength = QtWidgets.QComboBox(self.groupBoxPSF)
        self.comboBoxPSFWavelength.setObjectName("comboBoxPSFWavelength")
        self.gridLayout_4.addWidget(self.comboBoxPSFWavelength, 0, 3, 1, 1)
        self.label_17 = QtWidgets.QLabel(self.groupBoxPSF)
        self.label_17.setObjectName("label_17")
        self.gridLayout_4.addWidget(self.label_17, 0, 2, 1, 1)
        self.comboBoxPSFSampling = QtWidgets.QComboBox(self.groupBoxPSF)
        self.comboBoxPSFSampling.setObjectName("comboBoxPSFSampling")
        self.gridLayout_4.addWidget(self.comboBoxPSFSampling, 0, 1, 1, 1)
        self.label_18 = QtWidgets.QLabel(self.groupBoxPSF)
        self.label_18.setObjectName("label_18")
        self.gridLayout_4.addWidget(self.label_18, 1, 0, 1, 1)
        self.comboBoxPSFRotation = QtWidgets.QComboBox(self.groupBoxPSF)
        self.comboBoxPSFRotation.setObjectName("comboBoxPSFRotation")
        self.gridLayout_4.addWidget(self.comboBoxPSFRotation, 1, 1, 1, 1)
        self.label_19 = QtWidgets.QLabel(self.groupBoxPSF)
        self.label_19.setObjectName("label_19")
        self.gridLayout_4.addWidget(self.label_19, 1, 2, 1, 1)
        self.comboBoxPSFField = QtWidgets.QComboBox(self.groupBoxPSF)
        self.comboBoxPSFField.setObjectName("comboBoxPSFField")
        self.gridLayout_4.addWidget(self.comboBoxPSFField, 1, 3, 1, 1)
        self.verticalLayout_4.addLayout(self.gridLayout_4)
        self.widgetPSF = QtWidgets.QWidget(self.groupBoxPSF)
        self.widgetPSF.setObjectName("widgetPSF")
        self.verticalLayout_4.addWidget(self.widgetPSF)
        self.verticalLayout_12.addWidget(self.groupBoxPSF)
        self.tabWidget.addTab(self.tabPSF, "")
        self.horizontalLayout_8.addWidget(self.tabWidget)
        self.horizontalLayout_8.setStretch(0, 1)
        self.horizontalLayout_8.setStretch(1, 1)
        self.verticalLayout_19.addLayout(self.horizontalLayout_8)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.tabWidget_2 = QtWidgets.QTabWidget(self.centralwidget)
        self.tabWidget_2.setObjectName("tabWidget_2")
        self.tabOptimization = QtWidgets.QWidget()
        self.tabOptimization.setObjectName("tabOptimization")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.tabOptimization)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout()
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.pushButtonLoadPredictionConfig = QtWidgets.QPushButton(self.tabOptimization)
        self.pushButtonLoadPredictionConfig.setEnabled(False)
        self.pushButtonLoadPredictionConfig.setObjectName("pushButtonLoadPredictionConfig")
        self.verticalLayout_13.addWidget(self.pushButtonLoadPredictionConfig)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_13.addItem(spacerItem1)
        self.pushButtonPrediction = QtWidgets.QPushButton(self.tabOptimization)
        self.pushButtonPrediction.setObjectName("pushButtonPrediction")
        self.verticalLayout_13.addWidget(self.pushButtonPrediction)
        self.horizontalLayout_10.addLayout(self.verticalLayout_13)
        self.groupBox_3 = QtWidgets.QGroupBox(self.tabOptimization)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_16 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_16.setObjectName("verticalLayout_16")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.pushButtonAddPredictionVariable = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButtonAddPredictionVariable.setObjectName("pushButtonAddPredictionVariable")
        self.horizontalLayout_4.addWidget(self.pushButtonAddPredictionVariable)
        self.pushButtonDeletePredictionVariable = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButtonDeletePredictionVariable.setObjectName("pushButtonDeletePredictionVariable")
        self.horizontalLayout_4.addWidget(self.pushButtonDeletePredictionVariable)
        self.pushButtonDeleteAllPredictionVariable = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButtonDeleteAllPredictionVariable.setObjectName("pushButtonDeleteAllPredictionVariable")
        self.horizontalLayout_4.addWidget(self.pushButtonDeleteAllPredictionVariable)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.verticalLayout_16.addLayout(self.horizontalLayout_4)
        self.tableWidgetPedictionVariables = QtWidgets.QTableWidget(self.groupBox_3)
        self.tableWidgetPedictionVariables.setObjectName("tableWidgetPedictionVariables")
        self.tableWidgetPedictionVariables.setColumnCount(0)
        self.tableWidgetPedictionVariables.setRowCount(0)
        self.verticalLayout_16.addWidget(self.tableWidgetPedictionVariables)
        self.horizontalLayout_10.addWidget(self.groupBox_3)
        self.groupBox_4 = QtWidgets.QGroupBox(self.tabOptimization)
        self.groupBox_4.setObjectName("groupBox_4")
        self.verticalLayout_20 = QtWidgets.QVBoxLayout(self.groupBox_4)
        self.verticalLayout_20.setObjectName("verticalLayout_20")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.radioButtonPredictionRMS = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButtonPredictionRMS.setChecked(True)
        self.radioButtonPredictionRMS.setObjectName("radioButtonPredictionRMS")
        self.horizontalLayout_9.addWidget(self.radioButtonPredictionRMS)
        self.radioButtonPredictionSpot = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButtonPredictionSpot.setObjectName("radioButtonPredictionSpot")
        self.horizontalLayout_9.addWidget(self.radioButtonPredictionSpot)
        self.radioButtonPredictionCustom = QtWidgets.QRadioButton(self.groupBox_4)
        self.radioButtonPredictionCustom.setObjectName("radioButtonPredictionCustom")
        self.horizontalLayout_9.addWidget(self.radioButtonPredictionCustom)
        self.verticalLayout_20.addLayout(self.horizontalLayout_9)
        spacerItem3 = QtWidgets.QSpacerItem(20, 136, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_20.addItem(spacerItem3)
        self.horizontalLayout_10.addWidget(self.groupBox_4)
        self.verticalLayout_21 = QtWidgets.QVBoxLayout()
        self.verticalLayout_21.setObjectName("verticalLayout_21")
        self.textBrowserPrediction = QtWidgets.QTextBrowser(self.tabOptimization)
        self.textBrowserPrediction.setObjectName("textBrowserPrediction")
        self.verticalLayout_21.addWidget(self.textBrowserPrediction)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem4)
        self.pushButtonExportPredictionResult = QtWidgets.QPushButton(self.tabOptimization)
        self.pushButtonExportPredictionResult.setObjectName("pushButtonExportPredictionResult")
        self.horizontalLayout_3.addWidget(self.pushButtonExportPredictionResult)
        self.verticalLayout_21.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_10.addLayout(self.verticalLayout_21)
        self.horizontalLayout_10.setStretch(1, 1)
        self.horizontalLayout_10.setStretch(2, 1)
        self.horizontalLayout_10.setStretch(3, 1)
        self.tabWidget_2.addTab(self.tabOptimization, "")
        self.tabSensitivity = QtWidgets.QWidget()
        self.tabSensitivity.setObjectName("tabSensitivity")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.tabSensitivity)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.verticalLayout_15 = QtWidgets.QVBoxLayout()
        self.verticalLayout_15.setObjectName("verticalLayout_15")
        self.pushButtonSensitivityConfig = QtWidgets.QPushButton(self.tabSensitivity)
        self.pushButtonSensitivityConfig.setObjectName("pushButtonSensitivityConfig")
        self.verticalLayout_15.addWidget(self.pushButtonSensitivityConfig)
        self.pushButtonLoadSensitivityConfig = QtWidgets.QPushButton(self.tabSensitivity)
        self.pushButtonLoadSensitivityConfig.setObjectName("pushButtonLoadSensitivityConfig")
        self.verticalLayout_15.addWidget(self.pushButtonLoadSensitivityConfig)
        spacerItem5 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_15.addItem(spacerItem5)
        self.pushButtonSensitivityAnalysis = QtWidgets.QPushButton(self.tabSensitivity)
        self.pushButtonSensitivityAnalysis.setObjectName("pushButtonSensitivityAnalysis")
        self.verticalLayout_15.addWidget(self.pushButtonSensitivityAnalysis)
        self.horizontalLayout_6.addLayout(self.verticalLayout_15)
        self.verticalLayout_17 = QtWidgets.QVBoxLayout()
        self.verticalLayout_17.setObjectName("verticalLayout_17")
        self.textBrowserSensitivity = QtWidgets.QTextBrowser(self.tabSensitivity)
        self.textBrowserSensitivity.setObjectName("textBrowserSensitivity")
        self.verticalLayout_17.addWidget(self.textBrowserSensitivity)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem6)
        self.pushButtonExportSensitivity = QtWidgets.QPushButton(self.tabSensitivity)
        self.pushButtonExportSensitivity.setObjectName("pushButtonExportSensitivity")
        self.horizontalLayout_5.addWidget(self.pushButtonExportSensitivity)
        self.verticalLayout_17.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_6.addLayout(self.verticalLayout_17)
        self.tabWidget_2.addTab(self.tabSensitivity, "")
        self.tabAssemblyTolerance = QtWidgets.QWidget()
        self.tabAssemblyTolerance.setObjectName("tabAssemblyTolerance")
        
        # 使用水平布局替代原来的垂直布局
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.tabAssemblyTolerance)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        
        # 添加左侧Original Tolerance组
        self.groupBoxOriginalTolerance = QtWidgets.QGroupBox(self.tabAssemblyTolerance)
        self.groupBoxOriginalTolerance.setObjectName("groupBoxOriginalTolerance")
        self.verticalLayout_24 = QtWidgets.QVBoxLayout(self.groupBoxOriginalTolerance)
        self.verticalLayout_24.setObjectName("verticalLayout_24")
        
        # 使用网格布局放置控件
        self.gridLayout_6 = QtWidgets.QGridLayout()
        self.gridLayout_6.setObjectName("gridLayout_6")
        
        # Load按钮
        self.pushButtonLoadTolerance = QtWidgets.QPushButton(self.groupBoxOriginalTolerance)
        self.pushButtonLoadTolerance.setObjectName("pushButtonLoadTolerance")
        self.gridLayout_6.addWidget(self.pushButtonLoadTolerance, 0, 0, 1, 2)
        
        # Start At Surface行
        self.labelStartSurface = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelStartSurface.setObjectName("labelStartSurface")
        self.gridLayout_6.addWidget(self.labelStartSurface, 1, 0, 1, 1)
        
        # 从下拉框改为整数输入框
        self.spinBoxStartSurface = QtWidgets.QSpinBox(self.groupBoxOriginalTolerance)
        self.spinBoxStartSurface.setObjectName("spinBoxStartSurface")
        self.spinBoxStartSurface.setMinimum(1)
        self.spinBoxStartSurface.setMaximum(999)
        self.spinBoxStartSurface.setValue(1)
        self.gridLayout_6.addWidget(self.spinBoxStartSurface, 1, 1, 1, 1)
        
        # Stop At Surface行
        self.labelStopSurface = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelStopSurface.setObjectName("labelStopSurface")
        self.gridLayout_6.addWidget(self.labelStopSurface, 2, 0, 1, 1)
        
        # 从下拉框改为整数输入框
        self.spinBoxStopSurface = QtWidgets.QSpinBox(self.groupBoxOriginalTolerance)
        self.spinBoxStopSurface.setObjectName("spinBoxStopSurface")
        self.spinBoxStopSurface.setMinimum(1)
        self.spinBoxStopSurface.setMaximum(999)
        self.spinBoxStopSurface.setValue(6)
        self.gridLayout_6.addWidget(self.spinBoxStopSurface, 2, 1, 1, 1)
        
        # Thickness行 - 新增
        self.labelThickness = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelThickness.setObjectName("labelThickness")
        self.gridLayout_6.addWidget(self.labelThickness, 3, 0, 1, 1)
        
        self.doubleSpinBoxThickness = QtWidgets.QDoubleSpinBox(self.groupBoxOriginalTolerance)
        self.doubleSpinBoxThickness.setObjectName("doubleSpinBoxThickness")
        self.doubleSpinBoxThickness.setDecimals(6)
        self.doubleSpinBoxThickness.setMinimum(-10000.0)
        self.doubleSpinBoxThickness.setMaximum(10000.0)
        self.doubleSpinBoxThickness.setSingleStep(0.1)
        self.gridLayout_6.addWidget(self.doubleSpinBoxThickness, 3, 1, 1, 1)
        
        # Decenter X行
        self.labelDecenterX = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelDecenterX.setObjectName("labelDecenterX")
        self.gridLayout_6.addWidget(self.labelDecenterX, 4, 0, 1, 1)
        
        self.doubleSpinBoxDecenterX = QtWidgets.QDoubleSpinBox(self.groupBoxOriginalTolerance)
        self.doubleSpinBoxDecenterX.setObjectName("doubleSpinBoxDecenterX")
        self.doubleSpinBoxDecenterX.setDecimals(6)
        self.doubleSpinBoxDecenterX.setMinimum(-10000.0)
        self.doubleSpinBoxDecenterX.setMaximum(10000.0)
        self.doubleSpinBoxDecenterX.setSingleStep(0.1)
        self.gridLayout_6.addWidget(self.doubleSpinBoxDecenterX, 4, 1, 1, 1)
        
        # Decenter Y行
        self.labelDecenterY = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelDecenterY.setObjectName("labelDecenterY")
        self.gridLayout_6.addWidget(self.labelDecenterY, 5, 0, 1, 1)
        
        self.doubleSpinBoxDecenterY = QtWidgets.QDoubleSpinBox(self.groupBoxOriginalTolerance)
        self.doubleSpinBoxDecenterY.setObjectName("doubleSpinBoxDecenterY")
        self.doubleSpinBoxDecenterY.setDecimals(6)
        self.doubleSpinBoxDecenterY.setMinimum(-10000.0)
        self.doubleSpinBoxDecenterY.setMaximum(10000.0)
        self.doubleSpinBoxDecenterY.setSingleStep(0.1)
        self.gridLayout_6.addWidget(self.doubleSpinBoxDecenterY, 5, 1, 1, 1)
        
        # Title X行
        self.labelTitleX = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelTitleX.setObjectName("labelTitleX")
        self.gridLayout_6.addWidget(self.labelTitleX, 6, 0, 1, 1)
        
        self.doubleSpinBoxTitleX = QtWidgets.QDoubleSpinBox(self.groupBoxOriginalTolerance)
        self.doubleSpinBoxTitleX.setObjectName("doubleSpinBoxTitleX")
        self.doubleSpinBoxTitleX.setDecimals(6)
        self.doubleSpinBoxTitleX.setMinimum(-10000.0)
        self.doubleSpinBoxTitleX.setMaximum(10000.0)
        self.doubleSpinBoxTitleX.setSingleStep(0.1)
        self.gridLayout_6.addWidget(self.doubleSpinBoxTitleX, 6, 1, 1, 1)
        
        # Title Y行
        self.labelTitleY = QtWidgets.QLabel(self.groupBoxOriginalTolerance)
        self.labelTitleY.setObjectName("labelTitleY")
        self.gridLayout_6.addWidget(self.labelTitleY, 7, 0, 1, 1)
        
        self.doubleSpinBoxTitleY = QtWidgets.QDoubleSpinBox(self.groupBoxOriginalTolerance)
        self.doubleSpinBoxTitleY.setObjectName("doubleSpinBoxTitleY")
        self.doubleSpinBoxTitleY.setDecimals(6)
        self.doubleSpinBoxTitleY.setMinimum(-10000.0)
        self.doubleSpinBoxTitleY.setMaximum(10000.0)
        self.doubleSpinBoxTitleY.setSingleStep(0.1)
        self.gridLayout_6.addWidget(self.doubleSpinBoxTitleY, 7, 1, 1, 1)
        
        # 设置列宽度
        self.gridLayout_6.setColumnStretch(1, 1)
        
        self.verticalLayout_24.addLayout(self.gridLayout_6)
        
        # 添加一个弹性空间，使控件集中在顶部
        spacerItem8 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_24.addItem(spacerItem8)
        
        # 将Original Tolerance组添加到水平布局
        self.horizontalLayout_15.addWidget(self.groupBoxOriginalTolerance)
        
        # 添加右侧Setting区域
        self.groupBox_5 = QtWidgets.QGroupBox(self.tabAssemblyTolerance)
        self.groupBox_5.setObjectName("groupBox_5")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.groupBox_5)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        
        # 使用网格布局放置控件
        self.gridLayout_5 = QtWidgets.QGridLayout()
        self.gridLayout_5.setObjectName("gridLayout_5")
        
        # 第一行：Evaluation Criterion和下拉菜单
        self.label_20 = QtWidgets.QLabel(self.groupBox_5)
        self.label_20.setObjectName("label_20")
        self.gridLayout_5.addWidget(self.label_20, 0, 0, 1, 1)
        
        self.comboBoxCriterion = QtWidgets.QComboBox(self.groupBox_5)
        self.comboBoxCriterion.setObjectName("comboBoxCriterion")
        self.comboBoxCriterion.addItem("")
        self.comboBoxCriterion.addItem("")
        self.comboBoxCriterion.addItem("")
        self.comboBoxCriterion.addItem("")
        self.gridLayout_5.addWidget(self.comboBoxCriterion, 0, 1, 1, 1)
        
        # Limit输入框和标签
        self.label_21 = QtWidgets.QLabel(self.groupBox_5)
        self.label_21.setObjectName("label_21")
        self.gridLayout_5.addWidget(self.label_21, 0, 2, 1, 1)
        
        self.lineEditLimit = QtWidgets.QLineEdit(self.groupBox_5)
        self.lineEditLimit.setObjectName("lineEditLimit")
        self.gridLayout_5.addWidget(self.lineEditLimit, 0, 3, 1, 1)
        
        # Check按钮
        self.pushButtonCheck = QtWidgets.QPushButton(self.groupBox_5)
        self.pushButtonCheck.setObjectName("pushButtonCheck")
        self.gridLayout_5.addWidget(self.pushButtonCheck, 0, 4, 1, 1)
        
        # 第二行：Sampling和MTF Frequency
        self.label_22 = QtWidgets.QLabel(self.groupBox_5)
        self.label_22.setObjectName("label_22")
        self.gridLayout_5.addWidget(self.label_22, 1, 0, 1, 1)
        
        self.comboBoxSampling = QtWidgets.QComboBox(self.groupBox_5)
        self.comboBoxSampling.setObjectName("comboBoxSampling")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.comboBoxSampling.addItem("")
        self.gridLayout_5.addWidget(self.comboBoxSampling, 1, 1, 1, 1)
        
        self.label_23 = QtWidgets.QLabel(self.groupBox_5)
        self.label_23.setObjectName("label_23")
        self.gridLayout_5.addWidget(self.label_23, 1, 2, 1, 1)
        
        self.lineEditMTFFrequency = QtWidgets.QLineEdit(self.groupBox_5)
        self.lineEditMTFFrequency.setObjectName("lineEditMTFFrequency")
        self.gridLayout_5.addWidget(self.lineEditMTFFrequency, 1, 3, 1, 1)
        
        # 设置列宽度
        self.gridLayout_5.setColumnStretch(1, 1)
        self.gridLayout_5.setColumnStretch(3, 1)
        
        # 设置MTF Frequency默认值
        self.lineEditMTFFrequency.setText("30")
        self.lineEditMTFFrequency.setEnabled(False)  # 默认禁用
        self.label_23.setEnabled(False)  # 标签也禁用
        
        # 添加comboBoxCriterion的选择变化事件处理
        self.comboBoxCriterion.currentIndexChanged.connect(self.onCriterionChanged)
        
        self.verticalLayout_23.addLayout(self.gridLayout_5)
        
        # 结果显示区域
        self.textBrowserAssembly = QtWidgets.QTextBrowser(self.groupBox_5)
        self.textBrowserAssembly.setObjectName("textBrowserAssembly")
        self.verticalLayout_23.addWidget(self.textBrowserAssembly)
        
        # 底部按钮布局
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem7)
        
        self.pushButtonAnalysis = QtWidgets.QPushButton(self.groupBox_5)
        self.pushButtonAnalysis.setObjectName("pushButtonAnalysis")
        self.horizontalLayout_14.addWidget(self.pushButtonAnalysis)
        
        self.verticalLayout_23.addLayout(self.horizontalLayout_14)
        
        # 将Setting组添加到水平布局
        self.horizontalLayout_15.addWidget(self.groupBox_5)
        
        # 设置水平布局中两个组的比例
        self.horizontalLayout_15.setStretch(0, 1)  # Original Tolerance占1份
        self.horizontalLayout_15.setStretch(1, 2)  # Setting占2份
        
        # 增加Limit显示框的宽度
        self.lineEditLimit.setMinimumWidth(150)
        # 增加第3列的伸缩比例
        self.gridLayout_5.setColumnStretch(3, 2)  # 将第3列从1增加到2
        
        self.tabWidget_2.addTab(self.tabAssemblyTolerance, "")
        self.horizontalLayout_7.addWidget(self.tabWidget_2)
        self.groupBox_2 = QtWidgets.QGroupBox(self.centralwidget)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.textBrowserLog = QtWidgets.QTextBrowser(self.groupBox_2)
        self.textBrowserLog.setObjectName("textBrowserLog")
        self.verticalLayout_14.addWidget(self.textBrowserLog)
        self.horizontalLayout_7.addWidget(self.groupBox_2)
        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(1, 1)
        self.verticalLayout_19.addLayout(self.horizontalLayout_7)
        self.verticalLayout_19.setStretch(1, 2)
        self.verticalLayout_19.setStretch(2, 1)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1183, 23))
        self.menubar.setObjectName("menubar")
        self.menuHelp = QtWidgets.QMenu(self.menubar)
        self.menuHelp.setObjectName("menuHelp")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.menubar.addAction(self.menuHelp.menuAction())

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        self.tabWidget_2.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "OpticalRealSim"))
        self.pushButtonOpenZemax.setText(_translate("MainWindow", "Open"))
        self.label.setText(_translate("MainWindow", "Filename"))
        self.groupBoxSystemData.setTitle(_translate("MainWindow", "SystemData"))
        self.groupBoxLensData.setTitle(_translate("MainWindow", "LensData"))
        self.groupBox.setTitle(_translate("MainWindow", "RealDataAnalysis"))
        self.pushButtonAdd.setText(_translate("MainWindow", "Add"))
        self.pushButtonDelete.setText(_translate("MainWindow", "Delete"))
        self.pushButtonUpdate.setText(_translate("MainWindow", "Update"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabInspection), _translate("MainWindow", "Inspection"))
        self.groupBoxSpotDiagram.setTitle(_translate("MainWindow", "SpotDiagram"))
        self.label_9.setText(_translate("MainWindow", "Ray Density"))
        self.label_8.setText(_translate("MainWindow", "Wavelength"))
        self.label_10.setText(_translate("MainWindow", "Field"))
        self.label_11.setText(_translate("MainWindow", "ReferTo"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabSpotDiagram), _translate("MainWindow", "Spot Diagram"))
        self.groupBoxWavefrontMap.setTitle(_translate("MainWindow", "WavefrontMap"))
        self.label_4.setText(_translate("MainWindow", "Rotation"))
        self.label_7.setText(_translate("MainWindow", "RMS"))
        self.label_3.setText(_translate("MainWindow", "Wavelength"))
        self.label_2.setText(_translate("MainWindow", "Sampling"))
        self.label_5.setText(_translate("MainWindow", "Field"))
        self.label_6.setText(_translate("MainWindow", "PV"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabWavefrontMap), _translate("MainWindow", "Wavefront Map"))
        self.groupBoxMTF.setTitle(_translate("MainWindow", "MTF"))
        self.label_13.setText(_translate("MainWindow", "Wavelength"))
        self.label_12.setText(_translate("MainWindow", "Sampling"))
        self.label_14.setText(_translate("MainWindow", "MaxFrequency"))
        self.label_15.setText(_translate("MainWindow", "Field"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabMTF), _translate("MainWindow", "MTF"))
        self.groupBoxPSF.setTitle(_translate("MainWindow", "PSF"))
        self.label_16.setText(_translate("MainWindow", "Sampling"))
        self.label_17.setText(_translate("MainWindow", "Wavelength"))
        self.label_18.setText(_translate("MainWindow", "Rotation"))
        self.label_19.setText(_translate("MainWindow", "Field"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabPSF), _translate("MainWindow", "PSF"))
        self.pushButtonLoadPredictionConfig.setText(_translate("MainWindow", "Load"))
        self.pushButtonPrediction.setText(_translate("MainWindow", "Optimize"))
        self.groupBox_3.setTitle(_translate("MainWindow", "Variables"))
        self.pushButtonAddPredictionVariable.setText(_translate("MainWindow", "Add"))
        self.pushButtonDeletePredictionVariable.setText(_translate("MainWindow", "Delete"))
        self.pushButtonDeleteAllPredictionVariable.setText(_translate("MainWindow", "DeleteAll"))
        self.groupBox_4.setTitle(_translate("MainWindow", "Objects"))
        self.radioButtonPredictionRMS.setText(_translate("MainWindow", "Wavefront"))
        self.radioButtonPredictionSpot.setText(_translate("MainWindow", "spot"))
        self.radioButtonPredictionCustom.setText(_translate("MainWindow", "Custom"))
        self.pushButtonExportPredictionResult.setText(_translate("MainWindow", "Export"))
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tabOptimization), _translate("MainWindow", "Optimization"))
        self.pushButtonSensitivityConfig.setText(_translate("MainWindow", "Config"))
        self.pushButtonLoadSensitivityConfig.setText(_translate("MainWindow", "Load"))
        self.pushButtonSensitivityAnalysis.setText(_translate("MainWindow", "Analysis"))
        self.pushButtonExportSensitivity.setText(_translate("MainWindow", "Export"))
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tabSensitivity), _translate("MainWindow", "Sensitivity"))
        self.groupBox_2.setTitle(_translate("MainWindow", "Log"))
        self.menuHelp.setTitle(_translate("MainWindow", "Help"))
        self.pushButtonAnalysis.setText(_translate("MainWindow", "Analysis"))
        self.groupBox_5.setTitle(_translate("MainWindow", "Setting"))
        self.label_20.setText(_translate("MainWindow", "Evaluation Criterion:"))
        self.comboBoxCriterion.setItemText(0, _translate("MainWindow", "RMS Spot Radius"))
        self.comboBoxCriterion.setItemText(1, _translate("MainWindow", "RMS Wavefront"))
        self.comboBoxCriterion.setItemText(2, _translate("MainWindow", "Geom. MTF Avg"))
        self.comboBoxCriterion.setItemText(3, _translate("MainWindow", "Diff. MTF Avg"))
        self.label_21.setText(_translate("MainWindow", "Limit:"))
        self.label_22.setText(_translate("MainWindow", "Sampling:"))
        self.comboBoxSampling.setItemText(0, _translate("MainWindow", "1"))
        self.comboBoxSampling.setItemText(1, _translate("MainWindow", "2"))
        self.comboBoxSampling.setItemText(2, _translate("MainWindow", "3"))
        self.comboBoxSampling.setItemText(3, _translate("MainWindow", "4"))
        self.comboBoxSampling.setItemText(4, _translate("MainWindow", "5"))
        self.comboBoxSampling.setItemText(5, _translate("MainWindow", "6"))
        self.comboBoxSampling.setItemText(6, _translate("MainWindow", "7"))
        self.comboBoxSampling.setItemText(7, _translate("MainWindow", "8"))
        self.comboBoxSampling.setItemText(8, _translate("MainWindow", "9"))
        self.comboBoxSampling.setItemText(9, _translate("MainWindow", "10"))
        self.label_23.setText(_translate("MainWindow", "MTF Frequency:"))
        self.pushButtonCheck.setText(_translate("MainWindow", "Check"))
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tabAssemblyTolerance), _translate("MainWindow", "Assembly Tolerance"))
        self.groupBoxOriginalTolerance.setTitle(_translate("MainWindow", "Original Tolerance"))
        self.labelStartSurface.setText(_translate("MainWindow", "Start At Surface:"))
        self.labelStopSurface.setText(_translate("MainWindow", "Stop At Surface:"))
        self.labelThickness.setText(_translate("MainWindow", "Thickness:"))
        self.labelDecenterX.setText(_translate("MainWindow", "Decenter X:"))
        self.labelDecenterY.setText(_translate("MainWindow", "Decenter Y:"))
        self.labelTitleX.setText(_translate("MainWindow", "Title X:"))
        self.labelTitleY.setText(_translate("MainWindow", "Title Y:"))
        self.pushButtonLoadTolerance.setText(_translate("MainWindow", "Load"))

    def onCriterionChanged(self, index):
        """联动控制MTF Frequency是否启用"""
        mtf_enabled = index >= 2  # index 2或3为MTF相关选项
        self.lineEditMTFFrequency.setEnabled(mtf_enabled)
        self.label_23.setEnabled(mtf_enabled)
        
        if mtf_enabled and not self.lineEditMTFFrequency.text():
            self.lineEditMTFFrequency.setText("30")
