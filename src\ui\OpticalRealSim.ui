<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1183</width>
    <height>821</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>OpticalRealSim</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_19" stretch="0,2,1">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="pushButtonOpenZemax">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Open</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Filename</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="lineEditZemaxFilename">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,1">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_18">
        <item>
         <widget class="QGroupBox" name="groupBoxSystemData">
          <property name="title">
           <string>SystemData</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <widget class="QTableWidget" name="tableWidgetSystemData"/>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxLensData">
          <property name="title">
           <string>LensData</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <widget class="QTableWidget" name="tableWidgetLensData"/>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="title">
           <string>RealDataAnalysis</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QPushButton" name="pushButtonAdd">
               <property name="text">
                <string>Add</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonDelete">
               <property name="text">
                <string>Delete</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="pushButtonUpdate">
               <property name="text">
                <string>Update</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTableWidget" name="tableWidgetRealData"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tabInspection">
         <attribute name="title">
          <string>Inspection</string>
         </attribute>
        </widget>
        <widget class="QWidget" name="tabSpotDiagram">
         <attribute name="title">
          <string>Spot Diagram</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_8">
          <item>
           <widget class="QGroupBox" name="groupBoxSpotDiagram">
            <property name="title">
             <string>SpotDiagram</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <layout class="QGridLayout" name="gridLayout_2">
               <item row="0" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="text">
                  <string>Ray Density</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="3">
                <widget class="QComboBox" name="comboBoxSpotWavelength"/>
               </item>
               <item row="0" column="2">
                <widget class="QLabel" name="label_8">
                 <property name="text">
                  <string>Wavelength</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QSpinBox" name="spinBoxSpotRayDensity"/>
               </item>
               <item row="1" column="2">
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>Field</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="3">
                <widget class="QComboBox" name="comboBoxSpotField"/>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_11">
                 <property name="text">
                  <string>ReferTo</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QComboBox" name="comboBoxSpotReferTo"/>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QSplitter" name="splitter_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <widget class="QWidget" name="widget" native="true"/>
               <widget class="QTableWidget" name="tableWidgetSpot"/>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabWavefrontMap">
         <attribute name="title">
          <string>Wavefront Map</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_10">
          <item>
           <widget class="QGroupBox" name="groupBoxWavefrontMap">
            <property name="title">
             <string>WavefrontMap</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <item>
              <layout class="QGridLayout" name="gridLayout">
               <item row="0" column="1">
                <widget class="QComboBox" name="comboBoxWavefrontMapSampling"/>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="text">
                  <string>Rotation</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="3">
                <widget class="QComboBox" name="comboBoxWavefrontMapField"/>
               </item>
               <item row="2" column="2">
                <widget class="QLabel" name="label_7">
                 <property name="text">
                  <string>RMS</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QComboBox" name="comboBoxWavefrontMapRotation"/>
               </item>
               <item row="0" column="3">
                <widget class="QComboBox" name="comboBoxWavefrontMapWavelength"/>
               </item>
               <item row="0" column="2">
                <widget class="QLabel" name="label_3">
                 <property name="text">
                  <string>Wavelength</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="text">
                  <string>Sampling</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="2">
                <widget class="QLabel" name="label_5">
                 <property name="text">
                  <string>Field</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="text">
                  <string>PV</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QLineEdit" name="lineEditPV">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                </widget>
               </item>
               <item row="2" column="3">
                <widget class="QLineEdit" name="lineEditRMS">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QFrame" name="frame">
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9"/>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabMTF">
         <attribute name="title">
          <string>MTF</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_11">
          <item>
           <widget class="QGroupBox" name="groupBoxMTF">
            <property name="title">
             <string>MTF</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <item>
              <layout class="QGridLayout" name="gridLayout_3">
               <item row="0" column="1">
                <widget class="QComboBox" name="comboBoxMTFSampling"/>
               </item>
               <item row="0" column="3">
                <widget class="QComboBox" name="comboBoxMTFWavelength"/>
               </item>
               <item row="0" column="2">
                <widget class="QLabel" name="label_13">
                 <property name="text">
                  <string>Wavelength</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_12">
                 <property name="text">
                  <string>Sampling</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_14">
                 <property name="text">
                  <string>MaxFrequency</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QSpinBox" name="spinBoxMTFFrequency"/>
               </item>
               <item row="1" column="2">
                <widget class="QLabel" name="label_15">
                 <property name="text">
                  <string>Field</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="3">
                <widget class="QComboBox" name="comboBoxMTFField"/>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QWidget" name="widgetMTF" native="true"/>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabPSF">
         <attribute name="title">
          <string>PSF</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_12">
          <item>
           <widget class="QGroupBox" name="groupBoxPSF">
            <property name="title">
             <string>PSF</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <layout class="QGridLayout" name="gridLayout_4">
               <item row="0" column="0">
                <widget class="QLabel" name="label_16">
                 <property name="text">
                  <string>Sampling</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="3">
                <widget class="QComboBox" name="comboBoxPSFWavelength"/>
               </item>
               <item row="0" column="2">
                <widget class="QLabel" name="label_17">
                 <property name="text">
                  <string>Wavelength</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QComboBox" name="comboBoxPSFSampling"/>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_18">
                 <property name="text">
                  <string>Rotation</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QComboBox" name="comboBoxPSFRotation"/>
               </item>
               <item row="1" column="2">
                <widget class="QLabel" name="label_19">
                 <property name="text">
                  <string>Field</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="3">
                <widget class="QComboBox" name="comboBoxPSFField"/>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QWidget" name="widgetPSF" native="true"/>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1">
      <item>
       <widget class="QTabWidget" name="tabWidget_2">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tabOptimization">
         <attribute name="title">
          <string>Prediction</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="0,1,1,1">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_13">
            <item>
             <widget class="QPushButton" name="pushButtonLoadPredictionConfig">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="text">
               <string>Load</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonPrediction">
              <property name="text">
               <string>Prediction</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_3">
            <property name="title">
             <string>Variables</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_16">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QPushButton" name="pushButtonAddPredictionVariable">
                 <property name="text">
                  <string>Add</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="pushButtonDeletePredictionVariable">
                 <property name="text">
                  <string>Delete</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="pushButtonDeleteAllPredictionVariable">
                 <property name="text">
                  <string>DeleteAll</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_4">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QTableWidget" name="tableWidgetPedictionVariables"/>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_4">
            <property name="title">
             <string>Objects</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_20">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <item>
                <widget class="QRadioButton" name="radioButtonPredictionRMS">
                 <property name="text">
                  <string>Wavefront</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="radioButtonPredictionSpot">
                 <property name="text">
                  <string>spot</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QRadioButton" name="radioButtonPredictionCustom">
                 <property name="text">
                  <string>Custom</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>136</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_21">
            <item>
             <widget class="QTextBrowser" name="textBrowserPrediction"/>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="pushButtonExportPredictionResult">
                <property name="text">
                 <string>Export</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabSensitivity">
         <attribute name="title">
          <string>Sensitivity</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_15">
            <item>
             <widget class="QPushButton" name="pushButtonSensitivityConfig">
              <property name="text">
               <string>Config</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonLoadSensitivityConfig">
              <property name="text">
               <string>Load</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_2">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonSensitivityAnalysis">
              <property name="text">
               <string>Analysis</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_17">
            <item>
             <widget class="QTextBrowser" name="textBrowserSensitivity"/>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <spacer name="horizontalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="pushButtonExportSensitivity">
                <property name="text">
                 <string>Export</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="title">
         <string>Log</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_14">
         <item>
          <widget class="QTextBrowser" name="textBrowserLog"/>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1183</width>
     <height>23</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
   </widget>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
