import json
import os

def save_dict_to_json(data, filename):
    """
    将字典数据保存到JSON文件中
    :param data: 要保存的字典数据
    :param filename: 保存的文件名
    :return: True表示保存成功，False表示保存失败
    """
    try:
        # 确保目录存在
        directory = os.path.dirname(filename)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
            
        # 写入JSON文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存JSON文件失败: {str(e)}")
        return False 