"""
这是一个使用Zemax OpticStudio API (ZOS-API)的示例程序。
该程序展示了如何：
1. 连接到Zemax OpticStudio
2. 打开示例文件
3. 设置和运行公差分析
4. 保存结果
"""

import clr
import os
import winreg
from itertools import islice
 
class PythonStandaloneApplication(object):
    class LicenseException(Exception):
        pass
    class ConnectionException(Exception):
        pass
    class InitializationException(Exception):
        pass
    class SystemNotPresentException(Exception):
        pass
 
    def __init__(self, path=None): 
        # determine location of ZOSAPI_NetHelper.dll & add as reference
        aKey = winreg.OpenKey(winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), r"Software\Zemax", 0, winreg.KEY_READ)
        zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
        NetHelper = os.path.join(os.sep, zemaxData[0], r'ZOS-API\Libraries\ZOSAPI_NetHelper.dll')
        winreg.CloseKey(aKey)
        clr.AddReference(NetHelper)
        import ZOSAPI_NetHelper
        
        # Find the installed version of OpticStudio
        if path is None:
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize()
        else:
            # Note -- uncomment the following line to use a custom initialization path
            isInitialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize(path)
        
        # determine the ZOS root directory
        if isInitialized:
            dir = ZOSAPI_NetHelper.ZOSAPI_Initializer.GetZemaxDirectory()
        else:
            raise PythonStandaloneApplication.InitializationException("无法定位Zemax OpticStudio。请尝试使用硬编码路径。")
 
        # add ZOS-API referencecs
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI.dll"))
        clr.AddReference(os.path.join(os.sep, dir, "ZOSAPI_Interfaces.dll"))
        import ZOSAPI
 
        # create a reference to the API namespace
        self.ZOSAPI = ZOSAPI
 
        # Create the initial connection class
        self.TheConnection = ZOSAPI.ZOSAPI_Connection()
 
        if self.TheConnection is None:
            raise PythonStandaloneApplication.ConnectionException("无法初始化与ZOSAPI的.NET连接")
 
        self.TheApplication = self.TheConnection.CreateNewApplication()
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("无法获取ZOSAPI应用程序")
 
        if self.TheApplication.IsValidLicenseForAPI == False:
            raise PythonStandaloneApplication.LicenseException("许可证对ZOSAPI使用无效")
 
        self.TheSystem = self.TheApplication.PrimarySystem
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("无法获取主系统")
 
    def __del__(self):
        if self.TheApplication is not None:
            self.TheApplication.CloseApplication()
            self.TheApplication = None
        
        self.TheConnection = None
    
    def OpenFile(self, filepath, saveIfNeeded):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("无法获取主系统")
        self.TheSystem.LoadFile(filepath, saveIfNeeded)
 
    def CloseFile(self, save):
        if self.TheSystem is None:
            raise PythonStandaloneApplication.SystemNotPresentException("无法获取主系统")
        self.TheSystem.Close(save)
 
    def SamplesDir(self):
        if self.TheApplication is None:
            raise PythonStandaloneApplication.InitializationException("无法获取ZOSAPI应用程序")
        return self.TheApplication.SamplesDir
 
    def ExampleConstants(self):
        if self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusType.PremiumEdition:
            return "Premium"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeProfessionalEdition:
            return "Professional"
        elif self.TheApplication.LicenseStatus == self.ZOSAPI.LicenseStatusTypeStandardEdition:
            return "Standard"
        else:
            return "Invalid"
    
    def reshape(self, data, x, y, transpose = False):
        """将System.Double[,]转换为用于绘图或后处理的2D列表
        
        Parameters
        ----------
        data      : 直接来自ZOS-API的System.Double[,]数据
        x         : 新2D列表的x宽度[使用var.GetLength(0)获取维度]
        y         : 新2D列表的y宽度[使用var.GetLength(1)获取维度]
        transpose : 转置数据；某些多维线系列数据需要
        
        Returns
        -------
        res       : 2D列表；可以直接用于Matplotlib或使用numpy.asarray(res)转换为numpy数组
        """
        if type(data) is not list:
            data = list(data)
        var_lst = [y] * x
        it = iter(data)
        res = [list(islice(it, i)) for i in var_lst]
        if transpose:
            return self.transpose(res)
        return res
    
    def transpose(self, data):
        """转置2D列表（Python3.x或更高版本）。
        
        用于转换多维线系列（例如FFT PSF）
        
        Parameters
        ----------
        data      : Python原生列表（如果使用System.Data[,]对象，请先重塑）
        
        Returns
        -------
        res       : 转置后的2D列表
        """
        if type(data) is not list:
            data = list(data)
        return list(map(list, zip(*data)))

def main():
    """主函数，演示ZOS-API的使用"""
    try:
        # 创建ZOS-API连接
        zos = PythonStandaloneApplication()
        
        # 加载局部变量
        ZOSAPI = zos.ZOSAPI
        TheApplication = zos.TheApplication
        TheSystem = zos.TheSystem
        
        # 创建示例目录
        if not os.path.exists(TheApplication.SamplesDir + "\\API\\Python"):
            os.makedirs(TheApplication.SamplesDir + "\\API\\Python")
        
        # 打开Double Gauss示例文件
        samplesFolder = TheApplication.SamplesDir
        DGfile = samplesFolder + r"\Sequential\Objectives\Double Gauss 28 degree field.zos"
        TheSystem.LoadFile(DGfile, False)
        print("已加载示例文件：Double Gauss")
        
        print(f"是否在顺序公差向导模式: {TheSystem.TDE.SEQToleranceWizard.IsSEQToleranceWizard}")
        # 清除现有的操作数
        if TheSystem.TDE.NumberOfOperands > 0:
            TheSystem.TDE.RemoveOperandsAt(1, TheSystem.TDE.NumberOfOperands)
            print("已清除现有操作数")
            print(f"现有的操作数数量: {TheSystem.TDE.NumberOfOperands}")
        # 设置公差向导并运行
        tWiz = TheSystem.TDE.SEQToleranceWizard
        print(f"参数表达式: {tWiz.IsSEQToleranceWizard}")
        # 指定不使用的公差
        tWiz.IsSurfaceRadiusUsed = False
        tWiz.IsSurfaceSandAIrregularityUsed = False
        tWiz.IsIndexUsed = False
        tWiz.IsIndexAbbePercentageUsed = False
        tWiz.IsSurfaceThicknessUsed = False
        # 指定表面公差
        tWiz.IsSurfaceRadiusUsed = True
        tWiz.SurfaceRadius = 0.1
        #tWiz.SurfaceThickness = 0.1
        #tWiz.SurfaceDecenterX = 0.1
        tWiz.SurfaceDecenterY = 0.1
        #tWiz.SurfaceTiltX = 0.2
        #tWiz.SurfaceTiltY = 0.2
        
        # 指定元件公差
        tWiz.ElementDecenterX = 0.11
        tWiz.ElementDecenterY = 0.12
        tWiz.ElementTiltXDegrees = 0.21
        tWiz.ElementTiltYDegrees = 0.22
        
        
        tWiz.OK()
        print("已设置公差参数")
        print(f"创建的操作数数量: {TheSystem.TDE.NumberOfOperands}")
        
        # 创建保存目录
        dirLoc = samplesFolder + "\\API\\Python\\e14_seq_tolerance"
        if not os.path.exists(dirLoc):
            os.makedirs(dirLoc)
            
        # 保存顺序文件
        fileNameSeq = dirLoc + "\\Double Gauss (seq).zos"
        TheSystem.SaveAs(fileNameSeq)
        print('已保存顺序文件: %s' % fileNameSeq)
        
        # 设置公差分析并运行
        tol = TheSystem.Tools.OpenTolerancing()
        # 选择敏感性模式
        tol.SetupMode = ZOSAPI.Tools.Tolerancing.SetupModes.Sensitivity
        # 选择准则和相关设置
        tol.Criterion = ZOSAPI.Tools.Tolerancing.Criterions.RMSSpotRadius
        tol.CriterionSampling = 3
        tol.CriterionComp = ZOSAPI.Tools.Tolerancing.CriterionComps.OptimizeAll_DLS
        tol.CriterionCycle = 2
        tol.CriterionField = ZOSAPI.Tools.Tolerancing.CriterionFields.UserDefined
        # 选择MC运行次数和要保存的文件数
        tol.NumberOfRuns = 20
        tol.NumberToSave = 20
        # 运行公差分析
        tol.RunAndWaitForCompletion()
        tol.Close()
        print("已完成公差分析")
        
        # 转换为非顺序模式
        convertNSmode = TheSystem.Tools.OpenConvertToNSCGroup()
        convertNSmode.ConvertFileToNSC = True
        convertNSmode.RunAndWaitForCompletion()
        convertNSmode.Close()
        # 保存非顺序文件
        fileNameNS = dirLoc + "\\Double Gauss (NS).zos"
        TheSystem.SaveAs(fileNameNS)
        print('已保存非顺序文件: %s' % fileNameNS)
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 清理OpticStudio连接
        if 'zos' in locals():
            del zos
            zos = None
        print("程序执行完成")

if __name__ == '__main__':
    main() 