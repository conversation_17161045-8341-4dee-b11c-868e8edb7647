1.敏感性分析中增加镜片间隔的敏感性分析
2.敏感性分析修改为可配置的模式******已经完成
3.预测部分将镜片的位姿(偏心，倾斜)可以作为变量
4.根据实测数据优化完成后对位姿设定一个装调公差-雲要在软件中给定公差范围-(首先根据实测数据进行优化，优化是一个位姿数值，在此基础上，对镜片的位姿设定一个偏差范围)
5.需要能够修改文件中的位姿参数，从而进行二次仿真
6.分工艺进行仿真，可以单独对装配过程中的某个光路进行分析
7.装配过程以RMS为优化目标
8.装配完成后以MTF作为评价指标
9.能够显示光路图
10.PSF数值显示
11.镜片名称--添加注释
12.可以设定视场
13.镜片的坐标显示


**************解决方案：**************
1.镜片间隔无法增加敏感性分析，只能通过优化来完成，优化到一定值后，对该值进行人为的调整
2.敏感性分析目前实现了镜片位姿和镜片面形敏感性分析的可配置分析***共计5种敏感性分析
3.通过在主次镜前后增加coordinate break,来实现镜片局部位姿的调整, 点击updata按钮，更新光学镜片仿真结果
4.使用inverse limit来实现镜片位姿公差范围的确定，注意：该代码需要手动更改代码中蒙特卡洛的次数为，至少为镜片数量^变量数量
2025.0414，
开发完成了以RMS spot 为目标的装配公差范围的求解
RMS WAVEFORNT的开发还未完成，MTF的也是未完成状态
